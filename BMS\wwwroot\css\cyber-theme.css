/* ===== 口袋世界科幻主题样式 v2.0 ===== */

/* ===== 1. CSS变量定义 ===== */
:root {
    /* 科幻配色方案 */
    --cyber-blue: #00d4ff;
    --cyber-purple: #8b5cf6;
    --cyber-pink: #ec4899;
    --cyber-green: #10b981;
    --cyber-orange: #f59e0b;
    --cyber-red: #ef4444;
    
    /* 深色背景系 */
    --dark-bg: #0a0a0f;
    --dark-card: #1a1a2e;
    --dark-surface: #16213e;
    
    /* 文字颜色 */
    --text-primary: #e2e8f0;
    --text-secondary: #94a3b8;
    --text-muted: #64748b;
    
    /* 效果 */
    --neon-glow: 0 0 20px rgba(0, 212, 255, 0.5);
    --card-glow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --border-radius: 12px;
}

/* ===== 2. 基础重置 ===== */
* {
    box-sizing: border-box;
}

/* 强制覆盖Bootstrap样式 */
body {
    background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%) !important;
    background-attachment: fixed !important;
    color: #e2e8f0 !important;
}

.container-fluid {
    background: transparent !important;
}

/* 强制覆盖所有文本颜色 */
.container-fluid * {
    color: var(--text-primary);
}

.container-fluid input,
.container-fluid select,
.container-fluid textarea {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: var(--text-primary) !important;
}

/* 强制覆盖所有卡片背景 */
.container-fluid .card,
.container-fluid .cyber-card {
    background: linear-gradient(135deg, var(--dark-card) 0%, var(--dark-surface) 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: var(--text-primary) !important;
}

/* 强制覆盖表格样式 */
.container-fluid table,
.container-fluid .table {
    background: transparent !important;
    color: var(--text-primary) !important;
}

.container-fluid table th,
.container-fluid .table th {
    background: linear-gradient(135deg, var(--dark-card), var(--dark-surface)) !important;
    border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
    color: var(--text-primary) !important;
}

.container-fluid table td,
.container-fluid .table td {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: var(--text-primary) !important;
}

/* ===== 3. 全局科幻背景 ===== */
.cyber-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

.cyber-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle at 1px 1px, rgba(0, 212, 255, 0.3) 1px, transparent 0);
    background-size: 50px 50px;
    animation: matrix 30s linear infinite;
    opacity: 0.1;
}

/* ===== 4. 基础组件样式 ===== */

/* 科幻卡片组件 */
.cyber-card {
    background: linear-gradient(135deg, var(--dark-card) 0%, var(--dark-surface) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    box-shadow: var(--card-glow);
    color: var(--text-primary);
}

.cyber-card:hover {
    border-color: var(--cyber-blue);
    box-shadow: var(--neon-glow), var(--card-glow);
    transform: translateY(-5px);
}

.cyber-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--cyber-blue), var(--cyber-purple));
}

.cyber-card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.cyber-card-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.cyber-card-body {
    padding: 0;
}

.cyber-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* 科幻按钮组件 */
.cyber-btn {
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border: none;
    border-radius: var(--border-radius);
    color: white;
    padding: 12px 24px;
    font-weight: 600;
    transition: var(--transition);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 14px;
}

.cyber-btn:hover {
    box-shadow: var(--neon-glow);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.cyber-btn:focus {
    outline: none;
    box-shadow: var(--neon-glow);
}

.cyber-btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

.cyber-btn-lg {
    padding: 16px 32px;
    font-size: 16px;
}

.cyber-btn-success {
    background: linear-gradient(135deg, var(--cyber-green), var(--cyber-blue));
}

.cyber-btn-warning {
    background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-pink));
}

.cyber-btn-danger {
    background: linear-gradient(135deg, var(--cyber-red), var(--cyber-purple));
}

.cyber-btn-outline {
    background: transparent;
    border: 1px solid var(--cyber-blue);
    color: var(--cyber-blue);
}

.cyber-btn-outline:hover {
    background: var(--cyber-blue);
    color: white;
}

/* 科幻表格组件 */
.cyber-table-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.cyber-table {
    width: 100%;
    color: var(--text-primary);
    background: transparent;
    border-collapse: collapse;
    margin: 0;
}

.cyber-table th {
    background: linear-gradient(135deg, var(--dark-card), var(--dark-surface));
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
    padding: 15px;
    font-weight: 600;
    text-align: left;
    color: var(--text-primary);
}

.cyber-table td {
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.cyber-table tr:hover {
    background: rgba(0, 212, 255, 0.1);
}

.cyber-table tr:last-child td {
    border-bottom: none;
}

/* 科幻徽章组件 */
.badge {
    display: inline-block;
    padding: 4px 12px;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 20px;
    border: 1px solid transparent;
    transition: var(--transition);
}

.badge-success {
    background: linear-gradient(135deg, var(--cyber-green), #059669);
    color: white;
    border-color: var(--cyber-green);
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

.badge-secondary {
    background: linear-gradient(135deg, #64748b, #475569);
    color: white;
    border-color: #64748b;
    box-shadow: 0 0 10px rgba(100, 116, 139, 0.3);
}

.badge-danger {
    background: linear-gradient(135deg, var(--cyber-red), #dc2626);
    color: white;
    border-color: var(--cyber-red);
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
}

.badge-warning {
    background: linear-gradient(135deg, var(--cyber-orange), #d97706);
    color: white;
    border-color: var(--cyber-orange);
    box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

/* 科幻文本样式 */
.text-muted {
    color: var(--text-muted) !important;
}

.text-light {
    color: var(--text-primary) !important;
}

.text-danger {
    color: var(--cyber-red) !important;
}

/* 科幻复选框样式 */
.form-check {
    margin-bottom: 1rem;
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
    margin-right: 0.5rem;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: var(--transition);
}

.form-check-input:checked {
    background-color: var(--cyber-blue);
    border-color: var(--cyber-blue);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.form-check-input:focus {
    border-color: var(--cyber-blue);
    box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
}

.form-check-label {
    color: var(--text-primary);
    margin-bottom: 0;
    cursor: pointer;
}

/* 科幻Bootstrap工具类覆盖 */
.d-flex {
    display: flex !important;
}

.gap-1 {
    gap: 0.25rem !important;
}

.gap-2 {
    gap: 0.5rem !important;
}

.flex-wrap {
    flex-wrap: wrap !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.align-items-center {
    align-items: center !important;
}

.text-center {
    text-align: center !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mt-3 {
    margin-top: 1rem !important;
}

.me-2 {
    margin-right: 0.5rem !important;
}

/* 科幻表单组件 */
.cyber-form-group {
    margin-bottom: 20px;
}

.cyber-form-label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.cyber-form-control {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 12px 16px;
    width: 100%;
    transition: var(--transition);
    font-size: 14px;
}

.cyber-form-control:focus {
    border-color: var(--cyber-blue);
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
    outline: none;
    background: rgba(255, 255, 255, 0.08);
}

.cyber-form-control::placeholder {
    color: var(--text-muted);
}

.cyber-form-select {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 12px 16px;
    width: 100%;
    transition: var(--transition);
}

.cyber-form-select:focus {
    border-color: var(--cyber-blue);
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
    outline: none;
}

/* 科幻下拉框选项样式 */
.cyber-form-control option {
    background: var(--dark-card) !important;
    color: var(--text-primary) !important;
    padding: 10px !important;
    border: none !important;
}

.cyber-form-control option:hover {
    background: rgba(0, 212, 255, 0.2) !important;
    color: var(--cyber-blue) !important;
}

.cyber-form-control option:checked,
.cyber-form-control option:selected {
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple)) !important;
    color: white !important;
}

/* 针对不同浏览器的下拉框样式 */
.cyber-form-control select option {
    background: var(--dark-card) !important;
    color: var(--text-primary) !important;
}

.cyber-form-control select option:hover {
    background: rgba(0, 212, 255, 0.2) !important;
    color: var(--cyber-blue) !important;
}

.cyber-form-control select option:checked {
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple)) !important;
    color: white !important;
}

/* 强制覆盖浏览器默认样式 */
select.cyber-form-control {
    background: rgba(255, 255, 255, 0.05) !important;
    color: var(--text-primary) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

select.cyber-form-control option {
    background-color: var(--dark-card) !important;
    color: var(--text-primary) !important;
    padding: 8px 12px !important;
}

select.cyber-form-control option:hover {
    background-color: rgba(0, 212, 255, 0.2) !important;
    color: var(--cyber-blue) !important;
}

select.cyber-form-control option:checked,
select.cyber-form-control option:selected {
    background-color: var(--cyber-blue) !important;
    color: white !important;
}

/* WebKit浏览器特殊处理 */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    select.cyber-form-control option {
        background: var(--dark-card);
        color: var(--text-primary);
    }

    select.cyber-form-control option:hover {
        background: rgba(0, 212, 255, 0.2);
        color: var(--cyber-blue);
    }
}

/* 自定义下拉框样式（备用方案） */
.cyber-select-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
}

.cyber-select-wrapper::after {
    content: '▼';
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    color: var(--cyber-blue);
    pointer-events: none;
    font-size: 12px;
}

.cyber-select-wrapper select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    color: var(--text-primary) !important;
    padding: 12px 40px 12px 16px !important;
    width: 100% !important;
    cursor: pointer;
}

.cyber-select-wrapper select:focus {
    border-color: var(--cyber-blue) !important;
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2) !important;
    outline: none !important;
}

/* 强制覆盖所有浏览器的option样式 */
.cyber-select-wrapper select option {
    background-color: var(--dark-card) !important;
    color: var(--text-primary) !important;
    padding: 8px 12px !important;
    border: none !important;
}

/* Firefox特殊处理 */
@-moz-document url-prefix() {
    .cyber-select-wrapper select option {
        background: var(--dark-card) !important;
        color: var(--text-primary) !important;
    }

    .cyber-select-wrapper select option:hover {
        background: rgba(0, 212, 255, 0.2) !important;
        color: var(--cyber-blue) !important;
    }
}

/* 科幻弹框组件 */
.cyber-modal .modal-content {
    background: linear-gradient(135deg, var(--dark-card), var(--dark-surface));
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 20px;
    color: var(--text-primary);
    box-shadow: var(--card-glow), var(--neon-glow);
}

.cyber-modal .modal-header {
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
    background: linear-gradient(135deg, var(--dark-card), var(--dark-surface));
    border-radius: 20px 20px 0 0;
}

.cyber-modal .modal-title {
    color: var(--text-primary);
    font-weight: 600;
}

.cyber-modal .modal-body {
    color: var(--text-primary);
}

.cyber-modal .modal-footer {
    border-top: 1px solid rgba(0, 212, 255, 0.3);
    background: linear-gradient(135deg, var(--dark-card), var(--dark-surface));
    border-radius: 0 0 20px 20px;
}

.cyber-modal .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.cyber-modal .btn-close:hover {
    opacity: 1;
}

/* 科幻分页组件 */
.cyber-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
}

.cyber-pagination .page-item {
    list-style: none;
}

.cyber-pagination .page-link {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 8px;
    text-decoration: none;
    transition: var(--transition);
}

.cyber-pagination .page-link:hover {
    background: var(--cyber-blue);
    border-color: var(--cyber-blue);
    color: white;
    text-decoration: none;
}

.cyber-pagination .page-item.active .page-link {
    background: var(--cyber-blue);
    border-color: var(--cyber-blue);
    color: white;
}

.cyber-pagination .page-item.disabled .page-link {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ===== 5. 动画效果 ===== */
@@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(10px) rotate(-1deg); }
}

@@keyframes matrix {
    0% { transform: translateY(0); }
    100% { transform: translateY(-50px); }
}

@@keyframes scan {
    0%, 100% { opacity: 0; transform: translateX(-100%); }
    50% { opacity: 1; transform: translateX(100%); }
}

@@keyframes glow {
    from { filter: brightness(1); }
    to { filter: brightness(1.2); }
}

@@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 6. 响应式设计 ===== */
@@media (max-width: 768px) {
    .cyber-card {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .cyber-btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .cyber-table th,
    .cyber-table td {
        padding: 10px;
        font-size: 14px;
    }
    
    .cyber-form-control {
        padding: 10px 14px;
    }
    
    .cyber-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .cyber-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
}

@@media (max-width: 576px) {
    .cyber-card {
        padding: 15px;
        border-radius: 15px;
    }
    
    .cyber-btn {
        padding: 8px 16px;
        font-size: 13px;
    }
    
    .cyber-table-container {
        overflow-x: auto;
    }
    
    .cyber-pagination {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .cyber-pagination .page-link {
        padding: 6px 10px;
        font-size: 12px;
    }
}
