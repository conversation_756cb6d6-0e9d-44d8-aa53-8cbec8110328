using BMS.Models.Common;
using BMS.Models.DTOs;
using BMS.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BMS.Controllers
{
    /// <summary>
    /// 用户宠物管理控制器
    /// </summary>
    [Authorize]
    [Route("[controller]")]
    public class UserPetController : Controller
    {
        private readonly IUserPetService _userPetService;
        private readonly IUserService _userService;
        private readonly IRealmService _realmService;
        private readonly IUserEquipmentService _userEquipmentService;

        public UserPetController(IUserPetService userPetService, IUserService userService, IRealmService realmService, IUserEquipmentService userEquipmentService)
        {
            _userPetService = userPetService;
            _userService = userService;
            _realmService = realmService;
            _userEquipmentService = userEquipmentService;
        }

        /// <summary>
        /// 用户宠物管理首页
        /// </summary>
        /// <returns>用户宠物管理页面</returns>
        [HttpGet]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 获取用户宠物列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost("GetList")]
        public async Task<IActionResult> GetList([FromBody] UserPetQueryDto queryDto)
        {
            try
            {
                var result = await _userPetService.GetUserPetsAsync(queryDto);
                return Json(new { code = 200, data = result.Data, total = result.TotalCount });
            }
            catch (Exception ex)
            {
                return Json(new { code = 500, message = $"获取用户宠物列表失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 根据ID获取用户宠物信息
        /// </summary>
        /// <param name="id">用户宠物ID</param>
        /// <returns>用户宠物信息</returns>
        [HttpGet("GetById/{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var userPet = await _userPetService.GetUserPetByIdAsync(id);
                if (userPet == null)
                {
                    return Json(ApiResult.Fail("用户宠物不存在"));
                }

                return Json(ApiResult.Ok(userPet));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取用户宠物信息失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 根据用户ID获取该用户的所有宠物
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户宠物列表</returns>
        [HttpGet("GetByUserId/{userId}")]
        public async Task<IActionResult> GetByUserId(int userId)
        {
            try
            {
                var userPets = await _userPetService.GetPetsByUserIdAsync(userId);
                return Json(ApiResult.Ok(userPets));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取用户宠物列表失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 创建用户宠物
        /// </summary>
        /// <param name="createDto">创建用户宠物DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost("Create")]
        public async Task<IActionResult> Create([FromBody] UserPetCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                // 检查用户携带宠物数量限制（最多3只）
                var userPets = await _userPetService.GetPetsByUserIdAsync(createDto.UserId);
                var carriedPets = userPets.Where(p => p.Status == "携带").ToList();

                // 如果新增的宠物状态是"携带"，需要检查数量限制
                if (createDto.Status == "携带" && carriedPets.Count >= 3)
                {
                    return Json(ApiResult.Fail("用户最多只能携带3只宠物，请先将其他宠物放入牧场"));
                }

                // 如果设置为主宠，必须是携带状态
                if (createDto.IsMain && createDto.Status != "携带")
                {
                    return Json(ApiResult.Fail("主宠物必须是携带状态"));
                }

                // 如果设置为主宠，检查是否会超过携带数量限制
                if (createDto.IsMain && createDto.Status == "携带" && carriedPets.Count >= 3)
                {
                    // 检查当前是否已有主宠，如果有则可以替换，不会增加数量
                    var hasMainPet = carriedPets.Any(p => p.IsMain);
                    if (!hasMainPet)
                    {
                        return Json(ApiResult.Fail("用户最多只能携带3只宠物，无法添加新的主宠物"));
                    }
                }

                var result = await _userPetService.CreateUserPetAsync(createDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"创建用户宠物失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 更新用户宠物信息
        /// </summary>
        /// <param name="updateDto">更新用户宠物DTO</param>
        /// <returns>更新结果</returns>
        [HttpPost("Update")]
        public async Task<IActionResult> Update([FromBody] UserPetUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                // 获取当前宠物信息
                var currentPet = await _userPetService.GetUserPetByIdAsync(updateDto.Id);
                if (currentPet == null)
                {
                    return Json(ApiResult.Fail("宠物不存在"));
                }

                // 获取用户所有宠物
                var userPets = await _userPetService.GetPetsByUserIdAsync(currentPet.UserId);
                var carriedPets = userPets.Where(p => p.Status == "携带" && p.Id != updateDto.Id).ToList();

                // 如果要更新为携带状态，检查数量限制
                if (updateDto.Status == "携带" && currentPet.Status != "携带")
                {
                    if (carriedPets.Count >= 3)
                    {
                        return Json(ApiResult.Fail("用户最多只能携带3只宠物，请先将其他宠物放入牧场"));
                    }
                }

                // 如果设置为主宠，必须是携带状态
                if (updateDto.IsMain && updateDto.Status != "携带")
                {
                    return Json(ApiResult.Fail("主宠物必须是携带状态"));
                }

                // 如果设置为主宠且当前不是主宠，检查携带数量限制
                if (updateDto.IsMain && !currentPet.IsMain)
                {
                    // 如果要设置为主宠但状态要改为携带，需要检查数量
                    if (updateDto.Status == "携带" && currentPet.Status != "携带" && carriedPets.Count >= 3)
                    {
                        return Json(ApiResult.Fail("用户最多只能携带3只宠物，无法设置为主宠物"));
                    }
                }

                // 如果要取消主宠状态，检查是否还有其他主宠
                if (!updateDto.IsMain && currentPet.IsMain)
                {
                    var otherMainPets = userPets.Where(p => p.IsMain && p.Id != updateDto.Id).ToList();
                    if (otherMainPets.Count == 0)
                    {
                        // 如果没有其他主宠，需要提醒用户
                        return Json(ApiResult.Fail("每个用户必须有一个主宠物，请先设置其他宠物为主宠物"));
                    }
                }

                var result = await _userPetService.UpdateUserPetAsync(updateDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"更新用户宠物失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 删除用户宠物
        /// </summary>
        /// <param name="deleteDto">删除用户宠物DTO</param>
        /// <returns>删除结果</returns>
        [HttpPost("Delete")]
        public async Task<IActionResult> Delete([FromBody] UserPetDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userPetService.DeleteUserPetAsync(deleteDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"删除用户宠物失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 检查用户是否已拥有指定宠物
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="petNo">宠物序号</param>
        /// <param name="excludeId">排除的用户宠物ID</param>
        /// <returns>是否已拥有</returns>
        [HttpGet("CheckUserPet")]
        public async Task<IActionResult> CheckUserPet(int userId, int petNo, int? excludeId = null)
        {
            try
            {
                var exists = await _userPetService.CheckUserPetExistsAsync(userId, petNo, excludeId);
                return Json(ApiResult.Ok(exists));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"检查用户宠物失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取宠物配置下拉选项
        /// </summary>
        /// <returns>宠物配置选项</returns>
        [HttpGet("GetPetConfigOptions")]
        public async Task<IActionResult> GetPetConfigOptions()
        {
            try
            {
                var options = await _userPetService.GetPetConfigOptionsAsync();
                return Json(ApiResult.Ok(options));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取宠物配置选项失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取用户下拉选项
        /// </summary>
        /// <returns>用户选项</returns>
        [HttpGet("GetUserOptions")]
        public async Task<IActionResult> GetUserOptions()
        {
            try
            {
                var queryDto = new UserQueryDto { Page = 1, PageSize = 1000 };
                var result = await _userService.GetUsersAsync(queryDto);
                var options = result.Data.Select(u => new { value = u.Id, label = u.Username }).ToList();
                return Json(ApiResult.Ok(options));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取用户选项失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取用户宠物的技能列表
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>技能列表</returns>
        [HttpGet("GetSkills/{userPetId}")]
        public async Task<IActionResult> GetSkills(int userPetId)
        {
            try
            {
                var skills = await _userPetService.GetUserPetSkillsAsync(userPetId);
                return Json(ApiResult.Ok(skills));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取宠物技能失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 为用户宠物添加技能
        /// </summary>
        /// <param name="manageDto">添加技能DTO</param>
        /// <returns>添加结果</returns>
        [HttpPost("AddSkill")]
        public async Task<IActionResult> AddSkill([FromBody] UserPetSkillManageDto manageDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userPetService.AddUserPetSkillAsync(manageDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"添加宠物技能失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 更新用户宠物技能等级
        /// </summary>
        /// <param name="skillId">技能记录ID</param>
        /// <param name="skillLevel">技能等级</param>
        /// <returns>更新结果</returns>
        [HttpPost("UpdateSkillLevel")]
        public async Task<IActionResult> UpdateSkillLevel(int skillId, int skillLevel)
        {
            try
            {
                var result = await _userPetService.UpdateUserPetSkillLevelAsync(skillId, skillLevel);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"更新技能等级失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 删除用户宠物技能
        /// </summary>
        /// <param name="deleteDto">删除技能DTO</param>
        /// <returns>删除结果</returns>
        [HttpPost("DeleteSkill")]
        public async Task<IActionResult> DeleteSkill([FromBody] UserPetSkillDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userPetService.DeleteUserPetSkillAsync(deleteDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"删除宠物技能失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 批量为用户宠物分配技能（根据宠物配置）
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>分配结果</returns>
        [HttpPost("AssignSkills")]
        public async Task<IActionResult> AssignSkills(int userPetId)
        {
            try
            {
                var result = await _userPetService.AssignSkillsByPetConfigAsync(userPetId);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"分配技能失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 宠物进化
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>进化结果</returns>
        [HttpPost("Evolve")]
        public async Task<IActionResult> Evolve(int userPetId)
        {
            try
            {
                var result = await _userPetService.EvolvePetAsync(userPetId);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"宠物进化失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 重置宠物属性
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>重置结果</returns>
        [HttpPost("ResetAttributes")]
        public async Task<IActionResult> ResetAttributes(int userPetId)
        {
            try
            {
                var result = await _userPetService.ResetPetAttributesAsync(userPetId);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"重置宠物属性失败：{ex.Message}"));
            }
        }

        #region 境界管理接口

        /// <summary>
        /// 获取境界列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost("GetRealmList")]
        public async Task<IActionResult> GetRealmList([FromBody] RealmQueryDto queryDto)
        {
            try
            {
                var result = await _realmService.GetRealmsAsync(queryDto);
                return Json(new { code = 200, data = result.Data, total = result.TotalCount });
            }
            catch (Exception ex)
            {
                return Json(new { code = 500, message = $"获取境界列表失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 根据境界ID获取境界信息
        /// </summary>
        /// <param name="realmId">境界ID</param>
        /// <returns>境界信息</returns>
        [HttpGet("GetRealmById")]
        public async Task<IActionResult> GetRealmById(int realmId)
        {
            try
            {
                var realm = await _realmService.GetRealmByIdAsync(realmId);
                if (realm == null)
                {
                    return Json(ApiResult.Fail("境界不存在"));
                }

                return Json(ApiResult.Ok(realm));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取境界信息失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 创建境界
        /// </summary>
        /// <param name="createDto">创建境界DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost("CreateRealm")]
        public async Task<IActionResult> CreateRealm([FromBody] RealmCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _realmService.CreateRealmAsync(createDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"创建境界失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 更新境界信息
        /// </summary>
        /// <param name="updateDto">更新境界DTO</param>
        /// <returns>更新结果</returns>
        [HttpPost("UpdateRealm")]
        public async Task<IActionResult> UpdateRealm([FromBody] RealmUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _realmService.UpdateRealmAsync(updateDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"更新境界失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 删除境界
        /// </summary>
        /// <param name="deleteDto">删除境界DTO</param>
        /// <returns>删除结果</returns>
        [HttpPost("DeleteRealm")]
        public async Task<IActionResult> DeleteRealm([FromBody] RealmDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _realmService.DeleteRealmAsync(deleteDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"删除境界失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取境界选项
        /// </summary>
        /// <returns>境界选项</returns>
        [HttpGet("GetRealmOptions")]
        public async Task<IActionResult> GetRealmOptions()
        {
            try
            {
                var realms = await _realmService.GetRealmOptionsAsync();
                var options = realms.Select(r => new { value = r.RealmName, label = r.RealmName, realmId = r.RealmId, realmName = r.RealmName }).ToList();
                return Json(ApiResult.Ok(options));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取境界选项失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取宠物状态选项
        /// </summary>
        /// <returns>宠物状态选项</returns>
        [HttpGet("GetStatusOptions")]
        public IActionResult GetStatusOptions()
        {
            try
            {
                var options = new[]
                {
                    new { value = "牧场", label = "牧场" },
                    new { value = "携带", label = "携带" },
                    new { value = "丢弃", label = "丢弃" }
                };
                return Json(ApiResult.Ok(options));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取宠物状态选项失败：{ex.Message}"));
            }
        }

        #endregion

        #region 用户装备管理

        /// <summary>
        /// 获取用户装备列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost("GetEquipmentList")]
        public async Task<IActionResult> GetEquipmentList([FromBody] UserEquipmentQueryDto queryDto)
        {
            try
            {
                var result = await _userEquipmentService.GetUserEquipmentsAsync(queryDto);
                return Json(new { code = 200, data = result.Data, total = result.TotalCount });
            }
            catch (Exception ex)
            {
                return Json(new { code = 500, message = $"获取用户装备列表失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 根据ID获取用户装备详情
        /// </summary>
        /// <param name="id">装备ID</param>
        /// <returns>装备详情</returns>
        [HttpGet("GetEquipmentById/{id}")]
        public async Task<IActionResult> GetEquipmentById(int id)
        {
            try
            {
                var equipment = await _userEquipmentService.GetUserEquipmentByIdAsync(id);
                if (equipment == null)
                {
                    return Json(ApiResult.Fail("装备不存在"));
                }

                return Json(ApiResult.Ok(equipment));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取装备详情失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 创建用户装备
        /// </summary>
        /// <param name="createDto">创建装备DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost("CreateEquipment")]
        public async Task<IActionResult> CreateEquipment([FromBody] UserEquipmentCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userEquipmentService.CreateUserEquipmentAsync(createDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"创建装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 更新用户装备
        /// </summary>
        /// <param name="updateDto">更新装备DTO</param>
        /// <returns>更新结果</returns>
        [HttpPost("UpdateEquipment")]
        public async Task<IActionResult> UpdateEquipment([FromBody] UserEquipmentUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userEquipmentService.UpdateUserEquipmentAsync(updateDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"更新装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 删除用户装备
        /// </summary>
        /// <param name="deleteDto">删除装备DTO</param>
        /// <returns>删除结果</returns>
        [HttpPost("DeleteEquipment")]
        public async Task<IActionResult> DeleteEquipment([FromBody] UserEquipmentDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userEquipmentService.DeleteUserEquipmentAsync(deleteDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"删除装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 强化装备
        /// </summary>
        /// <param name="strengthenDto">强化装备DTO</param>
        /// <returns>强化结果</returns>
        [HttpPost("StrengthenEquipment")]
        public async Task<IActionResult> StrengthenEquipment([FromBody] UserEquipmentStrengthenDto strengthenDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userEquipmentService.StrengthenEquipmentAsync(strengthenDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"强化装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 穿戴装备
        /// </summary>
        /// <param name="wearDto">穿戴装备DTO</param>
        /// <returns>穿戴结果</returns>
        [HttpPost("WearEquipment")]
        public async Task<IActionResult> WearEquipment([FromBody] UserEquipmentWearDto wearDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userEquipmentService.WearEquipmentAsync(wearDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"穿戴装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 脱下装备
        /// </summary>
        /// <param name="equipmentId">装备ID</param>
        /// <returns>脱下结果</returns>
        [HttpPost("UnwearEquipment")]
        public async Task<IActionResult> UnwearEquipment(int equipmentId)
        {
            try
            {
                var result = await _userEquipmentService.UnwearEquipmentAsync(equipmentId);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"脱下装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取装备类型选项
        /// </summary>
        /// <returns>装备类型选项列表</returns>
        [HttpGet("GetEquipmentTypeOptions")]
        public async Task<IActionResult> GetEquipmentTypeOptions()
        {
            try
            {
                var options = await _userEquipmentService.GetEquipmentTypeOptionsAsync();
                return Json(ApiResult.Ok(options));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取装备类型选项失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取装备配置选项
        /// </summary>
        /// <returns>装备配置选项列表</returns>
        [HttpGet("GetEquipmentConfigOptions")]
        public async Task<IActionResult> GetEquipmentConfigOptions()
        {
            try
            {
                var options = await _userEquipmentService.GetEquipmentConfigOptionsAsync();
                return Json(ApiResult.Ok(options));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取装备配置选项失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取用户已装备的装备列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>已装备的装备列表</returns>
        [HttpGet("GetUserEquippedItems/{userId}")]
        public async Task<IActionResult> GetUserEquippedItems(int userId)
        {
            try
            {
                var equipments = await _userEquipmentService.GetUserEquippedItemsAsync(userId);
                return Json(ApiResult.Ok(equipments));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取用户已装备列表失败：{ex.Message}"));
            }
        }

        #endregion
    }
} 