@{
    ViewData["Title"] = "地图详情管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<div id="mapDetailApp">
    <!-- 页面标题和面包屑导航 -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>地图详情管理</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">首页</a></li>
                        <li class="breadcrumb-item active">地图详情管理</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">地图详情列表</h5>
                            <div class="card-tools">
                                <button type="button" class="btn btn-primary" v-on:click="showCreateModal">
                                    <i class="fas fa-plus"></i> 新增地图详情
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 搜索区域 -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="search-map-id" class="form-label">选择地图</label>
                                    <select class="form-select" id="search-map-id" v-model="searchForm.mapId">
                                        <option value="">请选择地图</option>
                                        <option v-for="map in maps" v-bind:key="map.id" v-bind:value="map.mapId">
                                            {{ map.mapId }} - {{ map.mapName }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="search-type" class="form-label">地图类型</label>
                                    <input type="number" class="form-control" id="search-type" v-model="searchForm.type" placeholder="请输入地图类型">
                                </div>
                                <div class="col-md-3">
                                    <label>&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="button" class="btn btn-info" v-on:click="searchMapDetails">
                                            <i class="fas fa-search"></i> 搜索
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据表格 -->
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>ID</th>
                                            <th>地图ID</th>
                                            <th>地图名称</th>
                                            <th>限制成长</th>
                                            <th>限制等级</th>
                                            <th>限制钥匙</th>
                                            <th>金币范围</th>
                                            <th>元宝范围</th>
                                            <th>掉落范围</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 加载状态 -->
                                        <tr v-if="loading && mapDetails.length === 0">
                                            <td colspan="9" class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">加载中...</span>
                                                </div>
                                                <p class="mt-2 mb-0">正在加载数据...</p>
                                            </td>
                                        </tr>
                                        <!-- 空数据状态 -->
                                        <tr v-else-if="!loading && mapDetails.length === 0">
                                            <td colspan="9" class="text-center text-muted">
                                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                                <p class="mb-0">暂无地图详情数据</p>
                                            </td>
                                        </tr>
                                        <!-- 数据行 -->
                                        <tr v-for="item in mapDetails" v-bind:key="item.id">
                                            <td>{{ item.id }}</td>
                                            <td>{{ item.mapId }}</td>
                                            <td>{{ item.mapName }}</td>
                                            <td>{{ item.limitLevel }}</td>
                                            <td>
                                                <span class="badge" v-bind:class="item.limitKey ? 'bg-danger' : 'bg-success'">
                                                    {{ item.limitKey ? '限制' : '不限制' }}
                                                </span>
                                            </td>
                                            <td>{{ item.minGold }} - {{ item.maxGold }}</td>
                                            <td>{{ item.minYuanbao }} - {{ item.maxYuanbao }}</td>
                                            <td>{{ item.minDrop }} - {{ item.maxDrop }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-warning me-1" v-on:click="showEditModal(item)" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" v-on:click="deleteMapDetail(item.id)" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <span class="text-muted">
                                        显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} 到 
                                        {{ Math.min(pagination.page * pagination.pageSize, pagination.totalCount) }} 条，
                                        共 {{ pagination.totalCount }} 条记录
                                    </span>
                                </div>
                                <div class="col-md-6">
                                    <nav>
                                        <ul class="pagination justify-content-end">
                                            <li class="page-item" v-bind:class="{ disabled: pagination.page <= 1 }">
                                                <button class="page-link" v-on:click="changePage(pagination.page - 1)">上一页</button>
                                            </li>
                                            <li class="page-item" v-for="page in getPageNumbers()" v-bind:key="page" v-bind:class="{ active: page === pagination.page }">
                                                <button class="page-link" v-on:click="changePage(page)">{{ page }}</button>
                                            </li>
                                            <li class="page-item" v-bind:class="{ disabled: pagination.page >= pagination.totalPages }">
                                                <button class="page-link" v-on:click="changePage(pagination.page + 1)">下一页</button>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 新增/编辑模态框 -->
    <div class="modal fade" id="mapDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ modalTitle }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">选择地图 <span class="text-danger">*</span></label>
                                    <select class="form-select" v-model="formData.mapId" v-bind:class="{ 'is-invalid': formErrors.mapId }">
                                        <option value="">请选择地图</option>
                                        <option v-for="map in maps" v-bind:key="map.id" v-bind:value="map.mapId">
                                            {{ map.mapId }} - {{ map.mapName }}
                                        </option>
                                    </select>
                                    <div class="invalid-feedback" v-if="formErrors.mapId">{{ formErrors.mapId }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">地图类型</label>
                                    <input type="number" class="form-control" v-model="formData.type">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">限制成长</label>
                                    <input type="number" class="form-control" v-model="formData.limitGrowth" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">限制等级</label>
                                    <input type="number" class="form-control" v-model="formData.limitLevel">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">限制钥匙</label>
                                    <div class="form-check mt-2">
                                        <input class="form-check-input" type="checkbox" v-model="formData.limitKey">
                                        <label class="form-check-label">限制钥匙</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">最小金币</label>
                                    <input type="number" class="form-control" v-model="formData.minGold">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">最大金币</label>
                                    <input type="number" class="form-control" v-model="formData.maxGold">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">最小元宝</label>
                                    <input type="number" class="form-control" v-model="formData.minYuanbao">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">最大元宝</label>
                                    <input type="number" class="form-control" v-model="formData.maxYuanbao">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">最小掉落</label>
                                    <input type="number" class="form-control" v-model="formData.minDrop">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">最大掉落</label>
                                    <input type="number" class="form-control" v-model="formData.maxDrop">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">地图图标</label>
                            <input type="text" class="form-control" v-model="formData.icon" placeholder="地图图标路径">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" v-on:click="saveMapDetail" v-bind:disabled="loading">
                        <span v-if="loading" class="spinner-border spinner-border-sm me-1"></span>
                        {{ isEdit ? '更新' : '保存' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

</div> <!-- 关闭 #mapDetailApp 容器 -->

<script src="~/lib/axios.min.js"></script>
<script>
console.log('MapDetail页面：开始初始化');

// 创建Vue应用 - 按照MapConfig的模式
const mapDetailVueApp = Vue.createApp({
    data() {
        return {
            mapDetails: [],
            maps: [], // 地图列表
            searchForm: {
                mapId: '',
                type: null
            },
            pagination: {
                page: 1,
                pageSize: 10,
                totalCount: 0,
                totalPages: 0
            },
            formData: {
                id: 0,
                mapId: '',
                limitGrowth: 0,
                limitLevel: 0,
                limitKey: false,
                minGold: 0,
                minYuanbao: 0,
                maxGold: 0,
                maxYuanbao: 0,
                maxDrop: 0,
                minDrop: 0,
                icon: '',
                type: 0
            },
            formErrors: {},
            loading: true,  // 初始化时设为true
            isEdit: false,
            modalTitle: ''
        };
    },
    mounted() {
        console.log('MapDetail Vue组件：已挂载，开始加载数据');
        this.loadMaps();
        this.loadMapDetails();
    },
    methods: {
        // 加载地图列表
        async loadMaps() {
            try {
                const response = await axios.get('/MapConfig/GetAll');
                if (response.data.success) {
                    this.maps = response.data.data;
                }
            } catch (error) {
                console.error('加载地图列表失败：', error);
            }
        },
        
        // 加载地图详情列表
        async loadMapDetails() {
            try {
                this.loading = true;
                console.log('正在加载地图详情列表...');
                
                const params = {
                    page: this.pagination.page,
                    pageSize: this.pagination.pageSize,
                    ...this.searchForm
                };
                
                const response = await axios.get('/MapDetail/GetPagedList', { params });
                console.log('地图详情列表响应：', response.data);
                
                if (response.data.success) {
                    this.mapDetails = response.data.data;
                    this.pagination.totalCount = response.data.totalCount;
                    this.pagination.totalPages = response.data.totalPages;
                    console.log('地图详情列表加载成功，数据条数：', this.mapDetails.length);
                } else {
                    console.error('加载数据失败：', response.data.message);
                    alert('加载数据失败：' + response.data.message);
                }
            } catch (error) {
                console.error('加载地图详情列表失败：', error);
                alert('加载数据失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 搜索
        searchMapDetails() {
            this.pagination.page = 1;
            this.loadMapDetails();
        },
        
        // 分页
        changePage(page) {
            if (page >= 1 && page <= this.pagination.totalPages) {
                this.pagination.page = page;
                this.loadMapDetails();
            }
        },
        
        // 获取页码数组
        getPageNumbers() {
            const pages = [];
            const start = Math.max(1, this.pagination.page - 2);
            const end = Math.min(this.pagination.totalPages, start + 4);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            return pages;
        },
        
        // 显示新增模态框
        showCreateModal() {
            console.log('showCreateModal clicked');
            this.isEdit = false;
            this.modalTitle = '新增地图详情';
            this.resetForm();
            // 使用Vue nextTick确保数据更新后再显示模态框
            this.$nextTick(() => {
                const modal = new bootstrap.Modal(document.getElementById('mapDetailModal'), {
                    backdrop: 'static',
                    keyboard: false
                });
                modal.show();
            });
        },
        
        // 显示编辑模态框
        showEditModal(item) {
            console.log('showEditModal clicked', item);
            this.isEdit = true;
            this.modalTitle = '编辑地图详情';
            this.formData = { ...item };
            this.formErrors = {};
            // 使用Vue nextTick确保数据更新后再显示模态框
            this.$nextTick(() => {
                const modal = new bootstrap.Modal(document.getElementById('mapDetailModal'), {
                    backdrop: 'static',
                    keyboard: false
                });
                modal.show();
            });
        },
        
        // 重置表单
        resetForm() {
            this.formData = {
                id: 0,
                mapId: '',
                limitGrowth: 0,
                limitLevel: 0,
                limitKey: false,
                minGold: 0,
                minYuanbao: 0,
                maxGold: 0,
                maxYuanbao: 0,
                maxDrop: 0,
                minDrop: 0,
                icon: '',
                type: 0
            };
            this.formErrors = {};
        },
        
        // 验证表单
        validateForm() {
            this.formErrors = {};
            
            if (!this.formData.mapId) {
                this.formErrors.mapId = '请选择地图';
            }
            
            return Object.keys(this.formErrors).length === 0;
        },
        
        // 保存地图详情
        async saveMapDetail() {
            if (!this.validateForm()) {
                return;
            }
            
            this.loading = true;
            
            try {
                const url = this.isEdit ? '/MapDetail/Update' : '/MapDetail/Create';
                const response = await axios.post(url, this.formData);
                
                if (response.data.success) {
                    alert(response.data.message);
                    // 关闭模态框
                    const modalElement = document.getElementById('mapDetailModal');
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    }
                    this.loadMapDetails();
                } else {
                    alert(response.data.message);
                }
            } catch (error) {
                console.error('保存地图详情失败：', error);
                alert('保存失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 删除地图详情
        async deleteMapDetail(id) {
            if (!confirm('确定要删除这个地图详情吗？')) {
                return;
            }
            
            try {
                const response = await axios.post('/MapDetail/Delete', { id });
                
                if (response.data.success) {
                    alert(response.data.message);
                    this.loadMapDetails();
                } else {
                    alert(response.data.message);
                }
            } catch (error) {
                console.error('删除地图详情失败：', error);
                alert('删除失败');
            }
        }
    }
});

// 确保DOM完全加载后挂载Vue应用
function mountVueApp() {
    const element = document.getElementById('mapDetailApp');
    const modal = document.getElementById('mapDetailModal');
    if (element && modal) {
        console.log('Mounting Vue app to #mapDetailApp');
        console.log('Modal element found:', modal);
        mapDetailVueApp.mount('#mapDetailApp');
    } else {
        console.error('Element not found! mapDetailApp:', !!element, 'modal:', !!modal);
        // 重试
        setTimeout(mountVueApp, 50);
    }
}

// 使用多种方式确保DOM准备就绪
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', mountVueApp);
} else {
    setTimeout(mountVueApp, 50);
}
</script> 