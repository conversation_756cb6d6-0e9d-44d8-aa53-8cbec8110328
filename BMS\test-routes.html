<!DOCTYPE html>
<html>
<head>
    <title>测试宠物进化配置路由</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>测试宠物进化配置路由</h1>
    <div id="results"></div>

    <script>
        const baseUrl = 'http://localhost:5078';
        const resultsDiv = document.getElementById('results');

        function log(message) {
            const p = document.createElement('p');
            p.textContent = message;
            resultsDiv.appendChild(p);
        }

        async function testRoutes() {
            // 测试GetList路由
            try {
                log('测试 GetList 路由...');
                const response = await axios.post(`${baseUrl}/PetEvolutionConfig/GetList`, {
                    page: 1,
                    pageSize: 10
                });
                log(`✅ GetList 成功: ${response.status}`);
            } catch (error) {
                log(`❌ GetList 失败: ${error.response?.status || error.message}`);
            }

            // 测试GetPetConfigOptions路由
            try {
                log('测试 GetPetConfigOptions 路由...');
                const response = await axios.get(`${baseUrl}/PetEvolutionConfig/GetPetConfigOptions`);
                log(`✅ GetPetConfigOptions 成功: ${response.status}`);
            } catch (error) {
                log(`❌ GetPetConfigOptions 失败: ${error.response?.status || error.message}`);
            }

            // 测试GetItemConfigOptions路由
            try {
                log('测试 GetItemConfigOptions 路由...');
                const response = await axios.get(`${baseUrl}/PetEvolutionConfig/GetItemConfigOptions`);
                log(`✅ GetItemConfigOptions 成功: ${response.status}`);
            } catch (error) {
                log(`❌ GetItemConfigOptions 失败: ${error.response?.status || error.message}`);
            }

            // 测试Index页面
            try {
                log('测试 Index 页面...');
                const response = await axios.get(`${baseUrl}/PetEvolutionConfig`);
                log(`✅ Index 页面成功: ${response.status}`);
            } catch (error) {
                log(`❌ Index 页面失败: ${error.response?.status || error.message}`);
            }
        }

        // 页面加载后开始测试
        window.onload = testRoutes;
    </script>
</body>
</html>
