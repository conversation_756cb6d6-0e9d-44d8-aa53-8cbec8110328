[2025-09-05 00:54:38.695 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 00:54:38.705 +08:00 INF] 开始测试数据库连接...
[2025-09-05 00:54:38.711 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 00:54:39.077 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 00:54:39.088 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 00:54:52.941 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 00:54:52.943 +08:00 INF] 开始测试数据库连接...
[2025-09-05 00:54:52.950 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 00:54:52.987 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 00:54:52.989 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 01:00:53.585 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-09-05 01:00:53.624 +08:00 DBG] 🔧 调试级别日志测试
[2025-09-05 01:00:53.648 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 01:00:53.652 +08:00 INF] 开始测试数据库连接...
[2025-09-05 01:00:53.755 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:00:54.268 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:00:54.277 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 01:00:54.281 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-05 01:00:55.273 +08:00 WRN] Failed to determine the https port for redirect.
[2025-09-05 01:03:19.265 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 01:03:19.271 +08:00 INF] 开始测试数据库连接...
[2025-09-05 01:03:19.281 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:03:19.567 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:03:19.569 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 01:04:13.668 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 01:04:13.674 +08:00 INF] 开始测试数据库连接...
[2025-09-05 01:04:13.683 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:13.730 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:13.731 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 01:04:23.541 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 01:04:23.557 +08:00 INF] 开始测试数据库连接...
[2025-09-05 01:04:23.565 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:23.609 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:23.629 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 01:04:23.748 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1 | 参数: [@username0=HM]
[2025-09-05 01:04:23.814 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1
[2025-09-05 01:04:23.967 +08:00 DBG] 执行SQL: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 ) | 参数: [@constant0=2025/9/5 1:04:23, @constant1=2025/9/5 1:04:23, @id2=1, @id=0]
[2025-09-05 01:04:24.106 +08:00 DBG] SQL执行完成: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 )
[2025-09-05 01:04:48.850 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 01:04:48.852 +08:00 INF] 开始测试数据库连接...
[2025-09-05 01:04:48.856 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:48.901 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:48.903 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 01:04:48.969 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 01:04:48.971 +08:00 INF] 开始测试数据库连接...
[2025-09-05 01:04:48.976 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:49.019 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:49.024 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 01:04:49.154 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 01:04:49.196 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 01:04:49.218 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 01:04:49.263 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 01:04:49.301 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 01:04:49.310 +08:00 INF] 开始测试数据库连接...
[2025-09-05 01:04:49.318 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:49.369 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:49.371 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 01:04:49.397 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 01:04:50.023 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 01:04:50.118 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 01:04:50.120 +08:00 INF] 开始测试数据库连接...
[2025-09-05 01:04:50.125 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:50.170 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:04:50.173 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 01:04:50.237 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 01:04:50.286 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 01:04:50.334 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 01:04:50.378 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 01:04:50.381 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 01:04:50.426 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 01:04:50.458 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 01:04:50.503 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 01:04:50.516 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 01:04:50.565 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 01:04:50.569 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 01:04:50.613 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 01:04:50.617 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 01:04:50.663 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 01:04:50.666 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 01:04:50.709 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 01:04:50.713 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 01:04:50.755 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 01:04:50.760 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 01:04:50.802 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 01:04:50.807 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 01:04:50.849 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 01:04:50.854 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-05 01:04:50.896 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 01:04:50.900 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=20, @MethodConst1=]
[2025-09-05 01:04:50.943 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 01:05:05.597 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 01:05:05.600 +08:00 INF] 开始测试数据库连接...
[2025-09-05 01:05:05.605 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:05:05.647 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 01:05:05.650 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 01:05:14.845 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-09-05 01:05:15.048 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 01:05:15.089 +08:00 DBG] 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )   LIMIT 0,1 | 参数: [@pet_no0=1]
[2025-09-05 01:05:15.141 +08:00 DBG] SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )   LIMIT 0,1
[2025-09-05 01:05:15.149 +08:00 DBG] 执行SQL: SELECT 1 FROM `user_pet`   WHERE (( `user_id` = @user_id0 ) AND ( `pet_no` = @pet_no1 ))   LIMIT 0,1 | 参数: [@user_id0=1, @pet_no1=1]
[2025-09-05 01:05:15.190 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user_pet`   WHERE (( `user_id` = @user_id0 ) AND ( `pet_no` = @pet_no1 ))   LIMIT 0,1
[2025-09-05 20:34:55.265 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-09-05 20:34:55.316 +08:00 DBG] 🔧 调试级别日志测试
[2025-09-05 20:34:55.346 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:34:55.573 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:34:55.774 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:34:56.330 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:34:56.341 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:34:56.344 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-05 20:34:58.601 +08:00 WRN] Failed to determine the https port for redirect.
[2025-09-05 20:35:21.373 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:35:21.376 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:35:21.389 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:35:21.422 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:35:21.424 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:35:45.163 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:35:45.165 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:35:45.174 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:35:45.206 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:35:45.208 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:35:45.331 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1 | 参数: [@username0=HM]
[2025-09-05 20:35:45.469 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1
[2025-09-05 20:35:45.701 +08:00 DBG] 执行SQL: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 ) | 参数: [@constant0=2025/9/5 20:35:45, @constant1=2025/9/5 20:35:45, @id2=1, @id=0]
[2025-09-05 20:35:45.811 +08:00 DBG] SQL执行完成: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 )
[2025-09-05 20:36:14.591 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:36:14.612 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:36:14.643 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:14.763 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:14.774 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:36:14.845 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:36:14.850 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:36:14.852 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:14.882 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:14.884 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:36:15.014 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 20:36:15.044 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 20:36:15.068 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 20:36:15.098 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 20:36:15.130 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:36:15.132 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:36:15.134 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:15.162 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:15.164 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:36:15.176 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 20:36:15.207 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 20:36:15.237 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:36:15.239 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:36:15.240 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:15.269 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:15.272 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:36:15.335 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:36:15.369 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:36:15.416 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 20:36:15.448 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:36:15.450 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 20:36:15.481 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 20:36:15.515 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 20:36:15.542 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:15.545 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 20:36:15.575 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:15.580 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 20:36:15.609 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:15.611 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 20:36:15.640 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:15.643 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 20:36:15.677 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:15.679 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 20:36:15.710 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:15.717 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 20:36:15.753 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:15.756 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 20:36:15.788 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:15.796 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-05 20:36:15.824 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:15.828 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=20, @MethodConst1=]
[2025-09-05 20:36:15.858 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:32.972 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:36:32.987 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:36:32.990 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:33.037 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:33.051 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:36:36.109 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-09-05 20:36:36.351 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 20:36:36.421 +08:00 DBG] 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )   LIMIT 0,1 | 参数: [@pet_no0=1]
[2025-09-05 20:36:36.451 +08:00 DBG] SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )   LIMIT 0,1
[2025-09-05 20:36:36.620 +08:00 DBG] 执行SQL: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID(); | 参数: [@user_id=1, @pet_no=1, @image=, @exp=10, @hp=100, @mp=100, @atk=10, @def=10, @spd=10, @state=0, @dodge=10, @name=, @growth=1.0, @hit=10, @deepen=, @offset=, @vamp=, @vamp_mp=, @custom_name=, @talisman_state=, @realm=, @evolve_count=0, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/5 20:36:36, @status=牧场, @is_main=False, @level=, @max_hp=, @max_mp=, @current_mp=]
[2025-09-05 20:36:36.774 +08:00 DBG] SQL执行完成: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID();
[2025-09-05 20:36:36.785 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=23]
[2025-09-05 20:36:36.820 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 20:36:36.829 +08:00 DBG] 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )   LIMIT 0,1 | 参数: [@pet_no0=1]
[2025-09-05 20:36:36.865 +08:00 DBG] SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )   LIMIT 0,1
[2025-09-05 20:36:39.915 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:36:39.915 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:36:39.917 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:39.944 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:36:39.949 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:36:39.953 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:36:39.983 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:36:39.986 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 20:36:40.016 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:36:40.019 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 20:36:40.052 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 20:36:40.056 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-05 20:36:40.085 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:40.088 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 20:36:40.119 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:40.123 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 20:36:40.153 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:40.155 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 20:36:40.183 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:40.185 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 20:36:40.213 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:40.216 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 20:36:40.247 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:40.248 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 20:36:40.276 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:40.278 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 20:36:40.305 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:40.323 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 20:36:40.363 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:36:40.468 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-05 20:36:40.496 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:37:55.096 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:37:55.105 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:37:55.107 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:37:55.136 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:37:55.138 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:37:55.208 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:37:55.210 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:37:55.213 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:37:55.242 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:37:55.244 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:37:55.247 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 20:37:55.278 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 20:37:55.284 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 20:37:55.318 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 20:37:55.407 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:37:55.463 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:37:55.543 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:37:55.576 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:37:55.580 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:37:55.582 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 20:37:55.654 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 20:37:55.838 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:37:55.993 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:37:56.071 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:37:56.118 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:37:56.133 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:37:56.139 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:37:56.234 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:37:56.240 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 20:37:56.275 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:37:56.283 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 20:37:56.317 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 20:37:56.324 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-05 20:37:56.357 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:37:56.371 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 20:37:56.423 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:37:56.431 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 20:37:56.497 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:37:56.515 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 20:37:56.586 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:37:56.674 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 20:37:56.804 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:37:56.833 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 20:37:56.878 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:37:56.902 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 20:37:57.021 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:37:57.026 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 20:37:57.229 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:37:57.272 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 20:37:57.325 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:37:57.357 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-05 20:37:57.443 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:38:41.381 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:38:41.385 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:38:41.387 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:38:41.416 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:38:41.417 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:38:41.437 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=17]
[2025-09-05 20:38:41.472 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 20:38:41.490 +08:00 DBG] 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=17, @user_id=1, @pet_no=4, @image=12, @exp=451000, @hp=100, @mp=10, @atk=10, @def=10, @spd=100, @state=, @dodge=100, @name=, @growth=5.24, @hit=100, @deepen=0.00, @offset=0.00, @vamp=0.00, @vamp_mp=0.00, @custom_name=, @talisman_state=, @realm=元神初具, @evolve_count=8, @synthesis_count=0, @nirvana_count=0, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=火, @create_time=2025/6/28 17:04:23, @status=牧场, @is_main=False, @level=1, @max_hp=, @max_mp=, @current_mp=10]
[2025-09-05 20:38:41.574 +08:00 DBG] SQL执行完成: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id
[2025-09-05 20:38:42.062 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:38:42.067 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:38:42.082 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:38:42.128 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:38:42.145 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:38:42.147 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:38:42.178 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:38:42.181 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 20:38:42.214 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:38:42.221 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 20:38:42.257 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 20:38:42.261 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-05 20:38:42.294 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:38:42.300 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 20:38:42.330 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:38:42.352 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 20:38:42.382 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:38:42.386 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 20:38:42.420 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:38:42.425 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 20:38:42.454 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:38:42.458 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 20:38:42.486 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:38:42.489 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 20:38:42.522 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:38:42.526 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 20:38:42.555 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:38:42.559 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 20:38:42.588 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:38:42.593 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-05 20:38:42.620 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:46:33.722 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-09-05 20:46:33.745 +08:00 DBG] 🔧 调试级别日志测试
[2025-09-05 20:46:33.749 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:46:33.751 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:46:33.795 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:46:34.217 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:46:34.225 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:46:34.226 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-05 20:46:51.743 +08:00 WRN] Failed to determine the https port for redirect.
[2025-09-05 20:46:51.988 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-09-05 20:46:52.205 +08:00 DBG] 🔧 调试级别日志测试
[2025-09-05 20:46:52.307 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:46:52.311 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:46:52.465 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:46:53.064 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:46:53.076 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:46:53.078 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-05 20:46:53.286 +08:00 ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5078: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
[2025-09-05 20:47:29.562 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-09-05 20:47:29.669 +08:00 DBG] 🔧 调试级别日志测试
[2025-09-05 20:47:29.740 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:47:29.754 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:47:29.930 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:30.464 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:30.486 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:47:30.502 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-05 20:47:31.814 +08:00 WRN] Failed to determine the https port for redirect.
[2025-09-05 20:47:37.309 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:47:37.311 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:47:37.317 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:37.358 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:37.360 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:47:37.444 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:47:37.447 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:47:37.450 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:37.488 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:37.490 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:47:37.611 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 20:47:37.659 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 20:47:37.688 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 20:47:37.728 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 20:47:37.781 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:47:37.782 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:47:37.784 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:37.825 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:37.827 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:47:37.838 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 20:47:37.878 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 20:47:37.916 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:47:37.918 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:47:37.920 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:37.959 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:37.961 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:47:38.010 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:47:38.050 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:47:57.770 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-09-05 20:47:57.942 +08:00 DBG] 🔧 调试级别日志测试
[2025-09-05 20:47:57.988 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:47:57.993 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:47:58.090 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:58.631 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:47:58.641 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:47:58.643 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-05 20:47:59.927 +08:00 WRN] Failed to determine the https port for redirect.
[2025-09-05 20:48:07.537 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:48:07.540 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:48:07.548 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:48:07.594 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:48:07.597 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:48:07.658 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:48:07.660 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:48:07.663 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:48:07.703 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:48:07.705 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:48:07.810 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 20:48:07.856 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 20:48:07.877 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 20:48:07.922 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 20:48:07.968 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:48:07.969 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:48:07.971 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:48:08.011 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:48:08.015 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:48:08.023 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 20:48:08.069 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 20:48:08.116 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:48:08.118 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:48:08.127 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:48:08.172 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:48:08.174 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:48:08.214 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:48:08.255 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:48:08.287 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 20:48:08.332 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:48:08.337 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 20:48:08.384 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 20:48:08.405 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-05 20:48:08.448 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:48:08.453 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 20:48:08.493 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:48:08.498 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 20:48:08.538 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:48:08.540 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 20:48:08.583 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:48:08.593 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 20:48:08.639 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:48:08.644 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 20:48:08.685 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:48:08.717 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 20:48:08.759 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:48:08.787 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 20:48:08.841 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:48:08.845 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 20:48:08.885 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:48:08.888 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-05 20:48:08.929 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:55:36.399 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-09-05 20:55:36.433 +08:00 DBG] 🔧 调试级别日志测试
[2025-09-05 20:55:36.451 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:55:36.453 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:55:36.640 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:55:37.197 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:55:37.208 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:55:37.211 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-05 20:55:38.093 +08:00 WRN] Failed to determine the https port for redirect.
[2025-09-05 20:55:51.674 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:55:51.710 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:55:51.718 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:55:51.769 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:55:51.780 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:56:06.151 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:56:06.153 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:56:06.156 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:06.204 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:06.212 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:56:06.272 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1 | 参数: [@username0=HM]
[2025-09-05 20:56:06.332 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1
[2025-09-05 20:56:06.417 +08:00 DBG] 执行SQL: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 ) | 参数: [@constant0=2025/9/5 20:56:06, @constant1=2025/9/5 20:56:06, @id2=1, @id=0]
[2025-09-05 20:56:07.005 +08:00 DBG] SQL执行完成: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 )
[2025-09-05 20:56:12.223 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:56:12.224 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:56:12.226 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:12.274 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:12.276 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:56:12.329 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:56:12.331 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:56:12.332 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:12.381 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:12.383 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:56:12.458 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 20:56:12.504 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 20:56:12.513 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 20:56:12.564 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 20:56:12.585 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:56:12.588 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:56:12.590 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:12.639 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:12.651 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:56:12.659 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 20:56:12.714 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 20:56:12.741 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:56:12.742 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:56:12.744 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:12.795 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:12.799 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:56:12.826 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:56:12.883 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:56:12.927 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 20:56:12.981 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:56:12.984 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 20:56:13.035 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 20:56:13.051 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-05 20:56:13.098 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:13.107 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 20:56:13.154 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:13.159 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 20:56:13.210 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:13.213 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 20:56:13.261 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:13.264 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 20:56:13.311 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:13.319 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 20:56:13.364 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:13.367 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 20:56:13.414 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:13.416 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 20:56:13.463 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:13.473 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 20:56:13.521 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:13.524 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-05 20:56:13.569 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:19.333 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 20:56:19.334 +08:00 INF] 开始测试数据库连接...
[2025-09-05 20:56:19.336 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:19.382 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 20:56:19.386 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 20:56:19.389 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:56:19.438 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:56:19.440 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 20:56:19.492 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 20:56:19.494 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 10,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 20:56:19.547 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 10,10
[2025-09-05 20:56:19.551 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=20, @MethodConst1=]
[2025-09-05 20:56:19.598 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:19.607 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=5, @MethodConst1=]
[2025-09-05 20:56:19.659 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:19.669 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=14, @MethodConst1=]
[2025-09-05 20:56:19.719 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 20:56:19.721 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-05 20:56:19.770 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:07:12.003 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-09-05 21:07:12.191 +08:00 DBG] 🔧 调试级别日志测试
[2025-09-05 21:07:12.240 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:12.249 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:12.359 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:12.875 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:12.885 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:12.888 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-05 21:07:13.962 +08:00 WRN] Failed to determine the https port for redirect.
[2025-09-05 21:07:16.406 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:16.412 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:16.418 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:16.469 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:16.472 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:16.540 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:16.651 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:16.654 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:16.700 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:16.703 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:16.792 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:07:16.851 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:07:16.873 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:07:16.917 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:07:16.962 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:16.964 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:16.966 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:17.008 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:17.010 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:17.019 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 21:07:17.063 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 21:07:17.099 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:17.100 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:17.102 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:17.146 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:17.147 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:17.193 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:07:17.237 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:07:17.263 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 21:07:17.312 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:07:17.315 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 21:07:17.363 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 21:07:17.382 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-05 21:07:17.424 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:07:17.427 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 21:07:17.469 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:07:17.473 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 21:07:17.516 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:07:17.533 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 21:07:17.578 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:07:17.591 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 21:07:17.639 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:07:17.643 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 21:07:17.684 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:07:17.687 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 21:07:17.732 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:07:17.738 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 21:07:17.784 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:07:17.787 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 21:07:17.832 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:07:17.836 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-05 21:07:17.878 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:07:28.464 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:28.493 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:28.500 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:28.543 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:28.572 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:28.588 +08:00 DBG] 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-05 21:07:28.691 +08:00 DBG] SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-05 21:07:28.711 +08:00 DBG] 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-05 21:07:28.759 +08:00 DBG] SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-05 21:07:28.870 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:28.873 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:28.875 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:28.919 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:28.920 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:28.943 +08:00 DBG] 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-05 21:07:29.177 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-05 21:07:29.181 +08:00 DBG] 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-05 21:07:29.968 +08:00 DBG] SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-05 21:07:32.560 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:32.565 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:32.568 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:32.611 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:32.615 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:32.685 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:32.725 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:32.742 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:32.792 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:32.796 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:32.799 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:07:32.842 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:07:32.848 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:07:32.892 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:07:32.902 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:32.903 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:32.905 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:32.955 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:32.956 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:32.960 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-09-05 21:07:33.008 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-09-05 21:07:33.056 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:33.057 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:33.058 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:33.101 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:33.104 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:33.114 +08:00 DBG] 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-09-05 21:07:33.154 +08:00 DBG] SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-09-05 21:07:33.165 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-09-05 21:07:33.216 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-09-05 21:07:33.224 +08:00 DBG] 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-09-05 21:07:33.275 +08:00 DBG] SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-09-05 21:07:33.284 +08:00 DBG] 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-09-05 21:07:33.334 +08:00 DBG] SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-05 21:07:33.347 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:33.349 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:33.352 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:33.394 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:33.397 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:33.404 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-05 21:07:33.454 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-05 21:07:33.457 +08:00 DBG] 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 21:07:33.510 +08:00 DBG] SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-09-05 21:07:40.279 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:40.281 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:40.283 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:40.326 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:40.328 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:40.388 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:40.388 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:40.390 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:40.392 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:40.394 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:40.395 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:40.437 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:40.439 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:40.443 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:07:40.488 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:07:40.489 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:07:40.535 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:07:40.545 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:40.546 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:40.549 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:40.590 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:40.595 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:40.600 +08:00 DBG] 执行SQL: SELECT  `equip_type_id` AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-09-05 21:07:40.626 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:40.629 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:40.642 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:07:40.643 +08:00 DBG] SQL执行完成: SELECT  `equip_type_id` AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-09-05 21:07:40.657 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:40.659 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:40.660 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:40.685 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:07:40.688 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-05 21:07:40.704 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:40.705 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:40.709 +08:00 DBG] 执行SQL: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-09-05 21:07:40.726 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-05 21:07:40.729 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-05 21:07:40.753 +08:00 DBG] SQL执行完成: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-09-05 21:07:40.768 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-05 21:07:40.771 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-05 21:07:40.809 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-05 21:07:40.821 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )   | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/5 21:07:40]
[2025-09-05 21:07:40.860 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )  
[2025-09-05 21:07:40.864 +08:00 DBG] 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/5 21:07:40, @MethodConst8=, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=0, @MethodConst13=0, @MethodConst14=False, @MethodConst15=2025/9/5 21:07:40]
[2025-09-05 21:07:40.908 +08:00 DBG] SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10
[2025-09-05 21:07:40.916 +08:00 DBG] 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092809, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-05 21:07:40.956 +08:00 DBG] SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-05 21:07:40.961 +08:00 DBG] 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789035, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-05 21:07:41.001 +08:00 DBG] SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-05 21:07:41.003 +08:00 DBG] 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092802, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-05 21:07:41.043 +08:00 DBG] SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-05 21:07:41.050 +08:00 DBG] 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789019, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-05 21:07:41.089 +08:00 DBG] SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-05 21:07:41.096 +08:00 DBG] 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2018102703, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-05 21:07:41.136 +08:00 DBG] SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-05 21:07:41.143 +08:00 DBG] 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2016121602, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-05 21:07:41.183 +08:00 DBG] SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-05 21:07:41.211 +08:00 DBG] 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2019042702, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-05 21:07:41.255 +08:00 DBG] SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-05 21:07:41.299 +08:00 DBG] 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2021010102, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-05 21:07:41.338 +08:00 DBG] SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-05 21:07:41.354 +08:00 DBG] 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789011, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-05 21:07:41.446 +08:00 DBG] SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-05 21:07:41.449 +08:00 DBG] 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789009, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-05 21:07:41.487 +08:00 DBG] SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-05 21:07:49.955 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:49.960 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:49.962 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:50.001 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:50.003 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:50.051 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:07:50.052 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:07:50.054 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:50.102 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:07:50.104 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:07:50.128 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `map_config`  
[2025-09-05 21:07:50.166 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `map_config`  
[2025-09-05 21:07:50.168 +08:00 DBG] 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-05 21:07:50.207 +08:00 DBG] SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-05 21:08:04.379 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:08:04.381 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:08:04.382 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:04.419 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:04.421 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:08:04.475 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:08:04.476 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:08:04.478 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:04.516 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:04.517 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:08:04.527 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-05 21:08:04.567 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-05 21:08:04.569 +08:00 DBG] 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-05 21:08:04.607 +08:00 DBG] SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-05 21:08:40.589 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:08:40.590 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:08:40.593 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:40.633 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:40.634 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:08:40.696 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:08:40.697 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:08:40.698 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:40.736 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:40.738 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:08:40.765 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:08:40.767 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:08:40.771 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:40.809 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:40.811 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:08:40.817 +08:00 DBG] 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-09-05 21:08:40.856 +08:00 DBG] SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-09-05 21:08:40.868 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:08:40.870 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:08:40.872 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:40.911 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:08:40.913 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:08:40.922 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-05 21:08:40.960 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-05 21:08:40.964 +08:00 DBG] 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-05 21:08:41.004 +08:00 DBG] SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-05 21:08:41.013 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-05 21:08:41.194 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:08:41.203 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-05 21:08:41.243 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:08:41.306 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-05 21:08:41.358 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:08:41.382 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-05 21:08:41.425 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:08:41.437 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-05 21:08:41.481 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:08:41.512 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-05 21:08:41.555 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:09:01.160 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:01.161 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:01.163 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:01.201 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:01.203 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:01.209 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:01.247 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:01.250 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,20
[2025-09-05 21:09:01.289 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,20
[2025-09-05 21:09:01.340 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:01.342 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:01.345 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:01.384 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:01.388 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:01.395 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:01.442 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:01.444 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-05 21:09:01.483 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-05 21:09:03.944 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:03.946 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:03.948 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:03.987 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:03.989 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:03.992 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-09-05 21:09:04.030 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 21:09:07.923 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:08.250 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:08.282 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:08.323 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:08.345 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:08.362 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:08.400 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:08.402 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,20
[2025-09-05 21:09:08.441 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,20
[2025-09-05 21:09:08.510 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:08.512 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:08.513 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:08.553 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:08.556 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:08.559 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:08.597 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:08.599 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-05 21:09:08.639 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-05 21:09:09.946 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:09.948 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:09.950 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:09.989 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:09.990 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:09.992 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-09-05 21:09:10.031 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 21:09:11.744 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:11.746 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:11.748 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:11.785 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:11.787 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:11.789 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:11.827 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:11.829 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,20
[2025-09-05 21:09:11.867 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,20
[2025-09-05 21:09:11.950 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:11.953 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:11.955 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:11.994 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:11.996 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:12.000 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:12.039 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-05 21:09:12.041 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-05 21:09:12.081 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-05 21:09:22.728 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:22.729 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:22.733 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:22.770 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:22.771 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:22.774 +08:00 DBG] 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-05 21:09:22.822 +08:00 DBG] SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-05 21:09:22.825 +08:00 DBG] 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-05 21:09:22.872 +08:00 DBG] SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-05 21:09:22.921 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:22.924 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:22.925 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:22.966 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:22.968 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:22.972 +08:00 DBG] 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-05 21:09:23.191 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-05 21:09:23.195 +08:00 DBG] 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-05 21:09:24.012 +08:00 DBG] SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-05 21:09:26.216 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:26.219 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:26.220 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:26.258 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:26.276 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:26.280 +08:00 DBG] 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active`,`update_time` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-09-05 21:09:26.329 +08:00 DBG] SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active`,`update_time` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 21:09:26.335 +08:00 DBG] 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-09-05 21:09:26.377 +08:00 DBG] SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-09-05 21:09:40.422 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:40.426 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:40.430 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:40.467 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:40.468 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:40.533 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:40.533 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:40.695 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:40.838 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:40.879 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:41.045 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:41.048 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:41.054 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:41.069 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:41.123 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:41.124 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:41.130 +08:00 DBG] 执行SQL: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-05 21:09:41.170 +08:00 DBG] SQL执行完成: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC 
[2025-09-05 21:09:41.273 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:41.277 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:41.283 +08:00 DBG] 执行SQL: SELECT  CAST(`item_no` AS CHAR) AS `ItemId` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`type`,@MethodConst1) AS `Type`  FROM `item_config` `x` ORDER BY `item_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-05 21:09:41.318 +08:00 DBG] SQL执行完成: SELECT  CAST(`item_no` AS CHAR) AS `ItemId` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`type`,@MethodConst1) AS `Type`  FROM `item_config` `x` ORDER BY `item_no` ASC 
[2025-09-05 21:09:41.326 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:41.331 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:41.347 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-05 21:09:41.516 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-05 21:09:41.521 +08:00 DBG] 执行SQL: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=1, @MethodConst6=1000, @MethodConst7=0.100, @MethodConst8=0.500, @MethodConst9=100.00, @is_active10=1, @constant11=True, @constant12=False, @MethodConst13=, @MethodConst14=2025/9/5 21:09:41, @MethodConst15=, @MethodConst16=, @MethodConst17=, @MethodConst18=, @MethodConst19=, @MethodConst20=1, @MethodConst21=1000, @MethodConst22=0.100, @MethodConst23=0.500, @MethodConst24=100.00, @is_active25=1, @constant26=True, @constant27=False, @MethodConst28=, @MethodConst29=2025/9/5 21:09:41]
[2025-09-05 21:09:41.846 +08:00 DBG] SQL执行完成: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 0,10
[2025-09-05 21:09:53.118 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:53.119 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:53.121 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:53.152 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:53.153 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:54.376 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:54.627 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:54.719 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:54.829 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:54.869 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:54.910 +08:00 DBG] 执行SQL: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-05 21:09:54.998 +08:00 DBG] SQL执行完成: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC 
[2025-09-05 21:09:55.079 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:09:55.223 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:09:55.377 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:55.626 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:09:55.723 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:09:55.858 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )   
[2025-09-05 21:09:55.986 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )   
[2025-09-05 21:09:56.146 +08:00 DBG] 执行SQL: SELECT  `psf`.`id` AS `Id` , `psf`.`main_pet_no` AS `MainPetNo` , IFNULL(`mpc`.`name`,concat('宠物', CAST(`psf`.`main_pet_no` AS CHAR),'')) AS `MainPetName` , IFNULL(`mpc`.`attribute`,@MethodConst15) AS `MainPetAttribute` , `psf`.`sub_pet_no` AS `SubPetNo` , IFNULL(`spc`.`name`,concat('宠物', CAST(`psf`.`sub_pet_no` AS CHAR),'')) AS `SubPetName` , IFNULL(`spc`.`attribute`,@MethodConst17) AS `SubPetAttribute` , IFNULL(`psf`.`main_growth_min`,@MethodConst18) AS `MainGrowthMin` , IFNULL(`psf`.`sub_growth_min`,@MethodConst19) AS `SubGrowthMin` , `psf`.`result_pet_no` AS `ResultPetNo` , IFNULL(`rpc`.`name`,concat('宠物', CAST(`psf`.`result_pet_no` AS CHAR),'')) AS `ResultPetName` , IFNULL(`rpc`.`attribute`,@MethodConst21) AS `ResultPetAttribute` , IFNULL(`psf`.`base_success_rate`,@MethodConst22) AS `BaseSuccessRate` , IFNULL(`psf`.`required_level`,@MethodConst23) AS `RequiredLevel` , IFNULL(`psf`.`cost_gold`,@MethodConst24) AS `CostGold` , IFNULL(`psf`.`formula_type`,@MethodConst25) AS `FormulaType` , IFNULL(CAST(`psf`.`is_active` as SIGNED),@MethodConst26) AS `IsActive` , IFNULL(`psf`.`create_time`,@MethodConst27) AS `CreateTime`  FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )     ORDER BY `psf`.`id` DESC LIMIT 0,10 | 参数: [@MethodConst1=未知, @MethodConst3=未知, @MethodConst4=0.000000, @MethodConst5=0.000000, @MethodConst7=未知, @MethodConst8=50.00, @MethodConst9=40, @MethodConst10=50000, @MethodConst11=FIXED, @MethodConst12=True, @MethodConst13=2025/9/5 21:09:56, @MethodConst15=未知, @MethodConst17=未知, @MethodConst18=0.000000, @MethodConst19=0.000000, @MethodConst21=未知, @MethodConst22=50.00, @MethodConst23=40, @MethodConst24=50000, @MethodConst25=FIXED, @MethodConst26=True, @MethodConst27=2025/9/5 21:09:56]
[2025-09-05 21:09:56.303 +08:00 DBG] SQL执行完成: SELECT  `psf`.`id` AS `Id` , `psf`.`main_pet_no` AS `MainPetNo` , IFNULL(`mpc`.`name`,concat('宠物', CAST(`psf`.`main_pet_no` AS CHAR),'')) AS `MainPetName` , IFNULL(`mpc`.`attribute`,@MethodConst15) AS `MainPetAttribute` , `psf`.`sub_pet_no` AS `SubPetNo` , IFNULL(`spc`.`name`,concat('宠物', CAST(`psf`.`sub_pet_no` AS CHAR),'')) AS `SubPetName` , IFNULL(`spc`.`attribute`,@MethodConst17) AS `SubPetAttribute` , IFNULL(`psf`.`main_growth_min`,@MethodConst18) AS `MainGrowthMin` , IFNULL(`psf`.`sub_growth_min`,@MethodConst19) AS `SubGrowthMin` , `psf`.`result_pet_no` AS `ResultPetNo` , IFNULL(`rpc`.`name`,concat('宠物', CAST(`psf`.`result_pet_no` AS CHAR),'')) AS `ResultPetName` , IFNULL(`rpc`.`attribute`,@MethodConst21) AS `ResultPetAttribute` , IFNULL(`psf`.`base_success_rate`,@MethodConst22) AS `BaseSuccessRate` , IFNULL(`psf`.`required_level`,@MethodConst23) AS `RequiredLevel` , IFNULL(`psf`.`cost_gold`,@MethodConst24) AS `CostGold` , IFNULL(`psf`.`formula_type`,@MethodConst25) AS `FormulaType` , IFNULL(CAST(`psf`.`is_active` as SIGNED),@MethodConst26) AS `IsActive` , IFNULL(`psf`.`create_time`,@MethodConst27) AS `CreateTime`  FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )     ORDER BY `psf`.`id` DESC LIMIT 0,10
[2025-09-05 21:10:17.082 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:17.484 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:17.547 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:17.578 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:17.579 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:17.629 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:17.630 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:17.631 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:17.661 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:17.663 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:17.675 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:17.677 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:17.679 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:17.708 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:17.710 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:17.713 +08:00 DBG] 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-09-05 21:10:17.742 +08:00 DBG] SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-09-05 21:10:17.750 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:17.751 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:17.753 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:17.783 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:17.785 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:17.791 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-05 21:10:17.820 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-05 21:10:17.823 +08:00 DBG] 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-05 21:10:17.857 +08:00 DBG] SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-05 21:10:17.860 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-05 21:10:17.894 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:17.896 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-05 21:10:17.927 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:17.930 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-05 21:10:17.962 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:17.964 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-05 21:10:17.995 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:17.997 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-05 21:10:18.028 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:18.030 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-05 21:10:18.059 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:48.361 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:48.372 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:48.375 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:48.406 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:48.525 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:48.677 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:48.679 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:48.682 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:48.717 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:48.719 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:48.740 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:48.742 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:48.744 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:48.774 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:48.776 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:48.779 +08:00 DBG] 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-09-05 21:10:48.811 +08:00 DBG] SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-09-05 21:10:48.819 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:48.820 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:48.823 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:48.856 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:48.861 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:48.864 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-05 21:10:48.895 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-05 21:10:48.896 +08:00 DBG] 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-05 21:10:48.926 +08:00 DBG] SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-05 21:10:48.929 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-05 21:10:48.959 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:48.961 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-05 21:10:48.991 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:48.994 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-05 21:10:49.026 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:49.030 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-05 21:10:49.065 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:49.068 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-05 21:10:49.096 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:49.099 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-05 21:10:49.126 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:10:53.115 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:53.120 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:53.135 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:53.165 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:53.232 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:53.277 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:53.279 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:53.280 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:53.311 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:53.370 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:53.386 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:10:53.436 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:10:53.437 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:10:53.467 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:10:53.472 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:53.473 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:53.475 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:53.502 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:53.504 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:53.507 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 21:10:53.536 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 21:10:53.560 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:10:53.561 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:10:53.563 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:53.590 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:10:53.592 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:10:53.594 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:10:53.624 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:10:53.627 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 21:10:53.657 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:10:53.660 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 21:10:53.693 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 21:10:53.696 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-05 21:10:53.725 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:10:53.729 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 21:10:53.757 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:10:53.768 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 21:10:53.795 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:10:53.797 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 21:10:53.824 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:10:53.826 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 21:10:53.853 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:10:53.855 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 21:10:53.904 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:10:53.909 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 21:10:53.937 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:10:53.939 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 21:10:53.969 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:10:53.971 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 21:10:54.002 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:10:54.004 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-05 21:10:54.030 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:14:12.137 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:14:12.141 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:14:12.143 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:14:12.367 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:14:12.375 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:14:12.395 +08:00 DBG] 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_001]
[2025-09-05 21:14:12.432 +08:00 DBG] SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-05 21:14:12.467 +08:00 DBG] 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_001, @task_name=新手指引1, @task_description=完成基础操作指引, @task_type=0, @is_repeatable=1, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"道具","Id":"1001","Amount":10},{"Type":"金币","Id":"gold","Amount":1000}], @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/9/5 21:14:12]
[2025-09-05 21:14:12.543 +08:00 DBG] SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-09-05 21:14:12.548 +08:00 DBG] 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_001]
[2025-09-05 21:14:12.581 +08:00 DBG] SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-09-05 21:14:12.588 +08:00 DBG] 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_001]
[2025-09-05 21:14:12.622 +08:00 DBG] SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-05 21:14:12.625 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-05 21:14:12.658 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:14:12.667 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:14:12.669 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:14:12.671 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:14:12.702 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:14:12.704 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:14:12.707 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-05 21:14:12.740 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-05 21:14:12.744 +08:00 DBG] 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-05 21:14:12.776 +08:00 DBG] SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-05 21:14:12.779 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-05 21:14:12.813 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:14:12.816 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-05 21:14:12.852 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:14:12.856 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-05 21:14:12.888 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:14:12.892 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-05 21:14:12.928 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:14:12.932 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-05 21:14:12.964 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:14:12.966 +08:00 DBG] 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-05 21:14:13.000 +08:00 DBG] SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-05 21:25:28.965 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-09-05 21:25:29.055 +08:00 DBG] 🔧 调试级别日志测试
[2025-09-05 21:25:29.103 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:25:29.123 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:25:29.248 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:29.675 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:29.685 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:25:29.689 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-05 21:25:30.904 +08:00 WRN] Failed to determine the https port for redirect.
[2025-09-05 21:25:33.958 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:25:33.962 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:25:33.966 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:33.995 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:33.997 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:25:34.053 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:25:34.055 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:25:34.059 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:34.091 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:34.092 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:25:34.178 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:25:34.210 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:25:34.232 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:25:34.260 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:25:34.306 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:25:34.307 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:25:34.309 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:34.337 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:34.339 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:25:34.346 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-09-05 21:25:34.386 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-09-05 21:25:34.439 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:25:34.441 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:25:34.443 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:34.469 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:34.471 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:25:34.479 +08:00 DBG] 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-09-05 21:25:34.503 +08:00 DBG] SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-09-05 21:25:34.513 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-09-05 21:25:34.539 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-09-05 21:25:34.588 +08:00 DBG] 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-09-05 21:25:34.623 +08:00 DBG] SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-09-05 21:25:34.651 +08:00 DBG] 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-09-05 21:25:34.690 +08:00 DBG] SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-05 21:25:34.707 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:25:34.708 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:25:34.710 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:34.736 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:34.742 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:25:34.765 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-05 21:25:34.801 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-05 21:25:34.806 +08:00 DBG] 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 21:25:34.848 +08:00 DBG] SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-09-05 21:25:40.440 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:25:40.441 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:25:40.444 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:40.469 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:40.470 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:25:40.525 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:25:40.527 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:25:40.529 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:40.554 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:40.557 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:25:40.561 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:25:40.588 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-05 21:25:40.590 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:25:40.617 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-05 21:25:40.629 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:25:40.630 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:25:40.635 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:40.662 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:40.663 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:25:40.668 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 21:25:40.696 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-05 21:25:40.742 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:25:40.743 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:25:40.744 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:40.776 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:40.785 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:25:40.827 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:25:40.887 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:25:40.904 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 21:25:40.936 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:25:40.942 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 21:25:40.974 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 21:25:41.069 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-05 21:25:41.118 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:25:41.141 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 21:25:41.168 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:25:41.175 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 21:25:41.201 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:25:41.218 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 21:25:41.247 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:25:41.259 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 21:25:41.289 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:25:41.307 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 21:25:41.338 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:25:41.341 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 21:25:41.366 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:25:41.369 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 21:25:41.394 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:25:41.397 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 21:25:41.425 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:25:41.427 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-05 21:25:41.453 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:25:47.817 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:25:47.821 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:25:47.823 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:47.849 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:25:47.853 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:25:47.877 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=23]
[2025-09-05 21:25:47.911 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 21:25:47.978 +08:00 DBG] 执行SQL: UPDATE `user_pet`  SET
             `is_main` = @is_main0    WHERE ((( `user_id` = @user_id1 ) AND ( `is_main` = @is_main2 )) AND ( `id` <> @id3 )) | 参数: [@is_main0=False, @user_id1=1, @is_main2=True, @id3=23, @id=0]
[2025-09-05 21:25:48.034 +08:00 DBG] SQL执行完成: UPDATE `user_pet`  SET
             `is_main` = @is_main0    WHERE ((( `user_id` = @user_id1 ) AND ( `is_main` = @is_main2 )) AND ( `id` <> @id3 ))
[2025-09-05 21:25:48.045 +08:00 DBG] 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=23, @user_id=1, @pet_no=1, @image=, @exp=10, @hp=100, @mp=, @atk=10, @def=10, @spd=10, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=, @offset=, @vamp=, @vamp_mp=, @custom_name=, @talisman_state=, @realm=, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/5 20:36:37, @status=牧场, @is_main=True, @level=, @max_hp=, @max_mp=, @current_mp=]
[2025-09-05 21:25:48.090 +08:00 ERR] 数据库操作发生错误: Column 'mp' cannot be null
SqlSugar.SqlSugarException: Column 'mp' cannot be null
[2025-09-05 21:26:18.055 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:26:18.064 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:26:18.067 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:26:18.094 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:26:18.099 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:26:18.107 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=23]
[2025-09-05 21:26:18.137 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 21:26:18.143 +08:00 DBG] 执行SQL: UPDATE `user_pet`  SET
             `is_main` = @is_main0    WHERE ((( `user_id` = @user_id1 ) AND ( `is_main` = @is_main2 )) AND ( `id` <> @id3 )) | 参数: [@is_main0=False, @user_id1=1, @is_main2=True, @id3=23, @id=0]
[2025-09-05 21:26:18.169 +08:00 DBG] SQL执行完成: UPDATE `user_pet`  SET
             `is_main` = @is_main0    WHERE ((( `user_id` = @user_id1 ) AND ( `is_main` = @is_main2 )) AND ( `id` <> @id3 ))
[2025-09-05 21:26:18.172 +08:00 DBG] 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=23, @user_id=1, @pet_no=1, @image=, @exp=10, @hp=100, @mp=, @atk=10, @def=10, @spd=10, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=, @offset=, @vamp=, @vamp_mp=, @custom_name=, @talisman_state=, @realm=, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/5 20:36:37, @status=牧场, @is_main=True, @level=, @max_hp=, @max_mp=, @current_mp=]
[2025-09-05 21:26:18.202 +08:00 ERR] 数据库操作发生错误: Column 'mp' cannot be null
SqlSugar.SqlSugarException: Column 'mp' cannot be null
[2025-09-05 21:26:35.078 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:26:35.105 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:26:35.123 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:26:35.216 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:26:35.218 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:26:35.222 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=23]
[2025-09-05 21:26:35.249 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 21:26:35.260 +08:00 DBG] 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=23, @user_id=1, @pet_no=1, @image=, @exp=10, @hp=100, @mp=, @atk=10, @def=10, @spd=10, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=, @offset=, @vamp=, @vamp_mp=, @custom_name=, @talisman_state=, @realm=, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/5 20:36:37, @status=牧场, @is_main=False, @level=, @max_hp=, @max_mp=, @current_mp=]
[2025-09-05 21:26:35.304 +08:00 ERR] 数据库操作发生错误: Column 'mp' cannot be null
SqlSugar.SqlSugarException: Column 'mp' cannot be null
[2025-09-05 21:26:40.646 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:26:40.654 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:26:40.657 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:26:40.685 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:26:40.687 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:26:40.689 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=23]
[2025-09-05 21:26:40.726 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 21:26:40.759 +08:00 DBG] 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=23, @user_id=1, @pet_no=1, @image=, @exp=10, @hp=100, @mp=, @atk=10, @def=10, @spd=10, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=, @offset=, @vamp=, @vamp_mp=, @custom_name=, @talisman_state=, @realm=, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/5 20:36:37, @status=牧场, @is_main=False, @level=, @max_hp=, @max_mp=, @current_mp=]
[2025-09-05 21:26:40.792 +08:00 ERR] 数据库操作发生错误: Column 'mp' cannot be null
SqlSugar.SqlSugarException: Column 'mp' cannot be null
[2025-09-05 21:27:55.199 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:27:55.253 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:27:55.256 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:27:55.284 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:27:55.285 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:27:55.288 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=23]
[2025-09-05 21:27:55.315 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 21:27:55.326 +08:00 DBG] 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=23, @user_id=1, @pet_no=1, @image=, @exp=10, @hp=100, @mp=, @atk=10, @def=10, @spd=10, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=, @offset=, @vamp=, @vamp_mp=, @custom_name=, @talisman_state=, @realm=, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/5 20:36:37, @status=牧场, @is_main=False, @level=, @max_hp=, @max_mp=, @current_mp=]
[2025-09-05 21:27:55.374 +08:00 DBG] SQL执行完成: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id
[2025-09-05 21:27:55.391 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:27:55.395 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:27:55.398 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:27:55.427 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:27:55.429 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:27:55.433 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:27:55.460 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:27:55.463 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 21:27:55.491 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:27:55.501 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 21:27:55.532 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 21:27:55.537 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-05 21:27:55.567 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:27:55.570 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 21:27:55.600 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:27:55.607 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 21:27:55.636 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:27:55.640 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 21:27:55.665 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:27:55.673 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 21:27:55.699 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:27:55.703 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 21:27:55.728 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:27:55.732 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 21:27:55.757 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:27:55.760 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 21:27:55.790 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:27:55.792 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 21:27:55.823 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:27:55.840 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-05 21:27:55.867 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:28:47.598 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:28:47.602 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:28:47.604 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:28:47.631 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:28:47.632 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:28:47.642 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-09-05 21:28:47.668 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 21:28:47.670 +08:00 DBG] 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )   LIMIT 0,1 | 参数: [@pet_no0=3]
[2025-09-05 21:28:47.697 +08:00 DBG] SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )   LIMIT 0,1
[2025-09-05 21:28:47.704 +08:00 DBG] 执行SQL: UPDATE `user_pet`  SET
             `is_main` = @is_main0    WHERE (( `user_id` = @user_id1 ) AND ( `is_main` = @is_main2 )) | 参数: [@is_main0=False, @user_id1=1, @is_main2=True, @id=0]
[2025-09-05 21:28:47.733 +08:00 DBG] SQL执行完成: UPDATE `user_pet`  SET
             `is_main` = @is_main0    WHERE (( `user_id` = @user_id1 ) AND ( `is_main` = @is_main2 ))
[2025-09-05 21:28:47.746 +08:00 DBG] 执行SQL: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID(); | 参数: [@user_id=1, @pet_no=3, @image=, @exp=10, @hp=100, @mp=100, @atk=10, @def=10, @spd=10, @state=0, @dodge=10, @name=, @growth=1.0, @hit=10, @deepen=, @offset=, @vamp=, @vamp_mp=, @custom_name=, @talisman_state=, @realm=, @evolve_count=0, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/5 21:28:47, @status=牧场, @is_main=True, @level=, @max_hp=, @max_mp=, @current_mp=]
[2025-09-05 21:28:47.829 +08:00 DBG] SQL执行完成: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID();
[2025-09-05 21:28:47.834 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=24]
[2025-09-05 21:28:47.862 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-05 21:28:47.869 +08:00 DBG] 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )   LIMIT 0,1 | 参数: [@pet_no0=3]
[2025-09-05 21:28:47.896 +08:00 DBG] SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )   LIMIT 0,1
[2025-09-05 21:28:47.905 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-05 21:28:47.907 +08:00 INF] 开始测试数据库连接...
[2025-09-05 21:28:47.910 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:28:47.940 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-05 21:28:47.941 +08:00 INF] ✅ 数据库连接测试成功
[2025-09-05 21:28:47.945 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:28:47.971 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:28:47.975 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-05 21:28:48.003 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-05 21:28:48.004 +08:00 DBG] 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-05 21:28:48.034 +08:00 DBG] SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-05 21:28:48.036 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-05 21:28:48.062 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:28:48.065 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-05 21:28:48.089 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:28:48.091 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-05 21:28:48.115 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:28:48.118 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-05 21:28:48.144 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:28:48.152 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-05 21:28:48.177 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:28:48.180 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-05 21:28:48.207 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:28:48.211 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-05 21:28:48.236 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:28:48.243 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-05 21:28:48.274 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:28:48.286 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-05 21:28:48.311 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-05 21:28:48.319 +08:00 DBG] 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-05 21:28:48.344 +08:00 DBG] SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
