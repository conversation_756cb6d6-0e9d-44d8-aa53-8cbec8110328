<!DOCTYPE html>
<html>
<head>
    <title>Test TaskConfig Delete</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>Test TaskConfig Delete API</h1>
    <button onclick="testDelete()">Test Delete API</button>
    <div id="result"></div>

    <script>
        async function testDelete() {
            try {
                const response = await axios.post('http://localhost:5001/TaskConfig/Delete', {
                    taskId: "TASK_20250827_111704_229"
                });
                
                document.getElementById('result').innerHTML = 
                    '<h3>Success!</h3><pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<h3>Error!</h3><pre>' + JSON.stringify(error.response?.data || error.message, null, 2) + '</pre>';
            }
        }
    </script>
</body>
</html>
