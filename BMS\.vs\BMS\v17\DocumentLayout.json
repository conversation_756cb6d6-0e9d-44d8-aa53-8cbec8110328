{"Version": 1, "WorkspaceRootPath": "D:\\AI OB\\HM_one_bg\\BMS\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\userpetservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\userpetservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\controllers\\userpetcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:controllers\\userpetcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\models\\entities\\user_pet.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:models\\entities\\user_pet.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\views\\auth\\login.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:views\\auth\\login.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\controllers\\taskconfigcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:controllers\\taskconfigcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\userequipmentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\userequipmentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\controllers\\useritemcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:controllers\\useritemcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\itemconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\itemconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\dropconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\dropconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\petsynthesisformulaservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\petsynthesisformulaservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\petevolutionconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\petevolutionconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\views\\petevolutionconfig\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:views\\petevolutionconfig\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\models\\dtos\\petevolutionconfigdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:models\\dtos\\petevolutionconfigdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "user_pet.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Entities\\user_pet.cs", "RelativeDocumentMoniker": "Models\\Entities\\user_pet.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Entities\\user_pet.cs", "RelativeToolTip": "Models\\Entities\\user_pet.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-05T14:04:17.153Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "UserPetService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\UserPetService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\UserPetService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\UserPetService.cs", "RelativeToolTip": "Services\\Implementations\\UserPetService.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABoAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T17:00:52.149Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "UserPetController.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\UserPetController.cs", "RelativeDocumentMoniker": "Controllers\\UserPetController.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\UserPetController.cs", "RelativeToolTip": "Controllers\\UserPetController.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAhwDAAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T13:52:55.88Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Login.cshtml", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\Auth\\Login.cshtml", "RelativeDocumentMoniker": "Views\\Auth\\Login.cshtml", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\Auth\\Login.cshtml", "RelativeToolTip": "Views\\Auth\\Login.cshtml", "ViewState": "AgIAANoCAAAAAAAAAAAcwOQCAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-09-04T07:08:55.226Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "Microsoft.Common.CurrentVersion.targets", "DocumentMoniker": "D:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "RelativeDocumentMoniker": "..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ToolTip": "D:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "RelativeToolTip": "..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ViewState": "AgIAAL4UAAAAAAAAAAAiwMcUAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003801|", "WhenOpened": "2025-09-03T15:47:48.595Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "PetSynthesisFormulaService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\PetSynthesisFormulaService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\PetSynthesisFormulaService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\PetSynthesisFormulaService.cs", "RelativeToolTip": "Services\\Implementations\\PetSynthesisFormulaService.cs", "ViewState": "AgIAAFUAAAAAAAAAAAAiwC4AAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-03T06:33:33.47Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "PetEvolutionConfigService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\PetEvolutionConfigService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\PetEvolutionConfigService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\PetEvolutionConfigService.cs", "RelativeToolTip": "Services\\Implementations\\PetEvolutionConfigService.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAnwCYAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-03T03:54:39.891Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "Index.cshtml", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\PetEvolutionConfig\\Index.cshtml", "RelativeDocumentMoniker": "Views\\PetEvolutionConfig\\Index.cshtml", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\PetEvolutionConfig\\Index.cshtml", "RelativeToolTip": "Views\\PetEvolutionConfig\\Index.cshtml", "ViewState": "AgIAAOsAAAAAAAAAAABowPUAAACaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-09-03T03:54:31.061Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "PetEvolutionConfigDto.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\DTOs\\PetEvolutionConfigDto.cs", "RelativeDocumentMoniker": "Models\\DTOs\\PetEvolutionConfigDto.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\DTOs\\PetEvolutionConfigDto.cs", "RelativeToolTip": "Models\\DTOs\\PetEvolutionConfigDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-03T03:50:43.625Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "TaskConfigController.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\TaskConfigController.cs", "RelativeDocumentMoniker": "Controllers\\TaskConfigController.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\TaskConfigController.cs", "RelativeToolTip": "Controllers\\TaskConfigController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAFMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-26T16:13:39.77Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "UserItemController.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\UserItemController.cs", "RelativeDocumentMoniker": "Controllers\\UserItemController.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\UserItemController.cs", "RelativeToolTip": "Controllers\\UserItemController.cs", "ViewState": "AgIAADQAAAAAAAAAAADwv1cAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-12T14:44:27.549Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "UserEquipmentService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\UserEquipmentService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\UserEquipmentService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\UserEquipmentService.cs", "RelativeToolTip": "Services\\Implementations\\UserEquipmentService.cs", "ViewState": "AgIAABQAAAAAAAAAAABQwDsAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T09:01:38.023Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "ItemConfigService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\ItemConfigService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\ItemConfigService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\ItemConfigService.cs", "RelativeToolTip": "Services\\Implementations\\ItemConfigService.cs", "ViewState": "AgIAACgCAAAAAAAAAAAIwDkCAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T08:04:00.377Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "DropConfigService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\DropConfigService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\DropConfigService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\DropConfigService.cs", "RelativeToolTip": "Services\\Implementations\\DropConfigService.cs", "ViewState": "AgIAAFwAAAAAAAAAAADgv2sAAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T08:58:41.244Z"}]}]}]}