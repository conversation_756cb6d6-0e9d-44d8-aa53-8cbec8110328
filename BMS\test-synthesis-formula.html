<!DOCTYPE html>
<html>
<head>
    <title>测试宠物合成公式接口</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>测试宠物合成公式接口</h1>
    <div id="results"></div>

    <script>
        const baseUrl = 'http://localhost:5078';
        const resultsDiv = document.getElementById('results');

        function log(message) {
            const p = document.createElement('p');
            p.innerHTML = message;
            resultsDiv.appendChild(p);
        }

        async function testAPIs() {
            // 测试数据库连接
            try {
                log('🔍 测试数据库连接...');
                const response = await axios.get(`${baseUrl}/PetSynthesisFormula/TestConnection`);
                log(`✅ 数据库连接成功: ${JSON.stringify(response.data)}`);
            } catch (error) {
                log(`❌ 数据库连接失败: ${error.response?.data?.message || error.message}`);
            }

            // 测试GetList接口
            try {
                log('<br>🔍 测试GetList接口...');
                const response = await axios.post(`${baseUrl}/PetSynthesisFormula/GetList`, {
                    page: 1,
                    pageSize: 10
                });
                log(`✅ GetList成功: 总数=${response.data.total}, 数据条数=${response.data.data?.length || 0}`);
                if (response.data.data && response.data.data.length > 0) {
                    log(`📋 第一条数据: ${JSON.stringify(response.data.data[0])}`);
                }
            } catch (error) {
                log(`❌ GetList失败: ${error.response?.data?.message || error.message}`);
                if (error.response?.data?.stackTrace) {
                    log(`📋 堆栈跟踪: <pre>${error.response.data.stackTrace}</pre>`);
                }
            }

            // 测试GetPetConfigOptions接口
            try {
                log('<br>🔍 测试GetPetConfigOptions接口...');
                const response = await axios.get(`${baseUrl}/PetSynthesisFormula/GetPetConfigOptions`);
                log(`✅ GetPetConfigOptions成功: 宠物选项数=${response.data.data?.length || 0}`);
                if (response.data.data && response.data.data.length > 0) {
                    log(`📋 前3个宠物选项: ${JSON.stringify(response.data.data.slice(0, 3))}`);
                }
            } catch (error) {
                log(`❌ GetPetConfigOptions失败: ${error.response?.data?.message || error.message}`);
            }

            // 测试Index页面
            try {
                log('<br>🔍 测试Index页面...');
                const response = await axios.get(`${baseUrl}/PetSynthesisFormula`);
                log(`✅ Index页面访问成功: ${response.status}`);
            } catch (error) {
                log(`❌ Index页面访问失败: ${error.response?.status || error.message}`);
            }

            // 测试创建公式（如果有宠物数据）
            try {
                log('<br>🔍 测试创建合成公式...');
                const response = await axios.post(`${baseUrl}/PetSynthesisFormula/Create`, {
                    mainPetNo: 1,
                    subPetNo: 2,
                    mainGrowthMin: 0.5,
                    subGrowthMin: 0.4,
                    resultPetNo: 3,
                    baseSuccessRate: 75.0,
                    requiredLevel: 50,
                    costGold: 100000,
                    formulaType: 'FIXED',
                    isActive: true
                });
                log(`✅ 创建合成公式成功: ${JSON.stringify(response.data)}`);
            } catch (error) {
                log(`❌ 创建合成公式失败: ${error.response?.data?.message || error.message}`);
            }
        }

        // 页面加载后开始测试
        window.onload = testAPIs;
    </script>
</body>
</html>
