<!DOCTYPE html>
<html>
<head>
    <title>测试宠物进化配置接口</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div id="app">
        <h1>测试宠物进化配置接口</h1>
        <div id="results"></div>
        
        <h2>Vue测试</h2>
        <p>当前时间: {{ currentTime }}</p>
        <p>计数器: {{ counter }}</p>
        <button @click="counter++">增加</button>

        <h2>API测试结果</h2>
        <div v-for="result in testResults" :key="result.name">
            <h3>{{ result.name }}</h3>
            <p v-if="result.success" style="color: green;">✅ {{ result.message }}</p>
            <p v-else style="color: red;">❌ {{ result.message }}</p>
            <pre v-if="result.data">{{ JSON.stringify(result.data, null, 2) }}</pre>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                currentTime: new Date().toLocaleString(),
                counter: 0,
                testResults: []
            },
            async mounted() {
                this.updateTime();
                setInterval(this.updateTime, 1000);
                await this.runTests();
            },
            methods: {
                updateTime() {
                    this.currentTime = new Date().toLocaleString();
                },
                
                async runTests() {
                    const baseUrl = 'http://localhost:5078';
                    
                    // 测试数据库连接
                    await this.testAPI('数据库连接测试', async () => {
                        const response = await axios.get(`${baseUrl}/PetEvolutionConfig/TestConnection`);
                        return { success: true, data: response.data };
                    });

                    // 测试GetList接口
                    await this.testAPI('GetList接口测试', async () => {
                        const response = await axios.post(`${baseUrl}/PetEvolutionConfig/GetList`, {
                            page: 1,
                            pageSize: 10
                        });
                        return { 
                            success: response.data.code === 200, 
                            data: {
                                total: response.data.total,
                                dataCount: response.data.data?.length || 0,
                                firstItem: response.data.data?.[0] || null
                            }
                        };
                    });

                    // 测试GetPetConfigOptions接口
                    await this.testAPI('GetPetConfigOptions接口测试', async () => {
                        const response = await axios.get(`${baseUrl}/PetEvolutionConfig/GetPetConfigOptions`);
                        return { 
                            success: response.data.code === 200, 
                            data: {
                                optionsCount: response.data.data?.length || 0,
                                firstOptions: response.data.data?.slice(0, 3) || []
                            }
                        };
                    });

                    // 测试GetItemConfigOptions接口
                    await this.testAPI('GetItemConfigOptions接口测试', async () => {
                        const response = await axios.get(`${baseUrl}/PetEvolutionConfig/GetItemConfigOptions`);
                        return { 
                            success: response.data.code === 200, 
                            data: {
                                optionsCount: response.data.data?.length || 0,
                                firstOptions: response.data.data?.slice(0, 3) || []
                            }
                        };
                    });
                },
                
                async testAPI(name, testFunc) {
                    try {
                        const result = await testFunc();
                        this.testResults.push({
                            name: name,
                            success: result.success,
                            message: result.success ? '测试通过' : '测试失败',
                            data: result.data
                        });
                    } catch (error) {
                        this.testResults.push({
                            name: name,
                            success: false,
                            message: `测试异常: ${error.response?.data?.message || error.message}`,
                            data: error.response?.data || null
                        });
                    }
                }
            }
        });
    </script>
</body>
</html>
