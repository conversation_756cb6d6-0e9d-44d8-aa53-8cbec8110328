@{
    ViewData["Title"] = "装备管理";
    Layout = "_Layout";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<style>
    /* 自定义按钮样式 */
    .btn-shadow {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border-radius: 6px;
    }
    
    .btn-shadow:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .btn-shadow:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .btn-action {
        border-radius: 4px;
        transition: all 0.2s ease;
        border: 2px solid;
    }
    
    .btn-action:hover {
        transform: scale(1.05);
    }
    
    .btn-outline-info:hover {
        background-color: #17a2b8;
        border-color: #17a2b8;
        color: white;
    }
    
    .btn-outline-warning:hover {
        background-color: #ffc107;
        border-color: #ffc107;
        color: #212529;
    }
    
    .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }
    
    /* 搜索区域按钮组优化 */
    .btn-group .btn {
        border-radius: 0;
    }
    
    .btn-group .btn:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
    }
    
    .btn-group .btn:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
    }
    
    /* 加载动画优化 */
    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }
    
    /* 分页按钮优化 */
    .pagination .page-link {
        border-radius: 4px;
        margin: 0 2px;
        transition: all 0.2s ease;
    }
    
    .pagination .page-link:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* 表格操作按钮容器 */
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
    }
    
    /* 响应式设计 */
    @@media (max-width: 768px) {
        .btn-group {
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
        }
        
        .btn-group .btn {
            border-radius: 6px !important;
            margin-bottom: 5px;
        }
        
        .btn-group .btn:last-child {
            margin-bottom: 0;
        }
    }
    
    /* 按钮文字对齐 */
    .btn i {
        vertical-align: middle;
    }
    
    /* 新增按钮特殊样式 */
    .btn-success.btn-shadow {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
    }
    
    .btn-success.btn-shadow:hover {
        background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
        border: none;
        color: white;
    }
    
    /* 主按钮特殊样式 */
    .btn-primary.btn-shadow {
        background: linear-gradient(135deg, #007bff 0%, #6c5ce7 100%);
        border: none;
        color: white;
    }
    
    .btn-primary.btn-shadow:hover {
        background: linear-gradient(135deg, #0056b3 0%, #5a4fcf 100%);
        border: none;
        color: white;
    }
    
    /* 禁用状态优化 */
    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }
    
    /* 卡片标题美化 */
    .card-title {
        font-weight: 600;
        color: #495057;
    }
    
    /* 搜索表单美化 */
    .search-form {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }
    
    .form-label i {
        font-size: 0.85rem;
    }
    
    .text-transparent {
        color: transparent !important;
    }
    
    .form-control-search {
        border: 2px solid #e9ecef;
        border-radius: 6px;
        transition: all 0.3s ease;
        font-size: 0.9rem;
        height: 38px;
    }
    
    .form-control-search:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        background-color: #fff;
    }
    
    .input-group-append .btn {
        border-left: none;
        border-color: #e9ecef;
        background: transparent;
        color: #6c757d;
    }
    
    .input-group-append .btn:hover {
        background-color: #e9ecef;
        color: #495057;
    }
    
    .btn-search {
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
        min-width: 45px;
        height: 38px;
    }
    
    .btn-search:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .search-buttons .btn-group {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-radius: 6px;
    }
    
    .search-buttons .btn-group .btn:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
    
    .search-buttons .btn-group .btn:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-left: none;
    }
    
    .search-stats {
        font-size: 0.9rem;
        font-weight: 500;
    }
    
    .action-buttons {
        text-align: right;
    }
    
    /* 搜索区域响应式设计 */
    @@media (max-width: 991.98px) {
        .search-form {
            padding: 15px;
        }
        
        .search-buttons .btn-group {
            width: 100%;
            margin-top: 10px;
        }
        
        .search-buttons .btn {
            flex: 1;
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 15px;
        }
    }
    
    @@media (max-width: 575.98px) {
        .form-label {
            font-size: 0.85rem;
        }
        
        .form-control-search {
            font-size: 0.85rem;
            height: 36px;
        }
        
        .btn-search {
            height: 36px;
            font-size: 0.85rem;
        }
        
        .search-stats {
            font-size: 0.8rem;
        }
    }
    
    /* 输入框聚焦动画 */
    .form-group {
        position: relative;
    }
    
    .form-control-search:focus + .input-group-append .btn {
        border-color: #007bff;
    }
    
    /* 清除按钮美化 */
    .input-group-append .btn i {
        font-size: 0.7rem;
    }
</style>

<!-- 面包屑导航 -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>装备管理</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">首页</a></li>
                    <li class="breadcrumb-item active">装备管理</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容 -->
<section class="content">
    <div class="container-fluid">
        <div id="equipmentApp">
            <!-- 选项卡导航 -->
            <div class="card card-primary card-tabs">
                <div class="card-header p-0 pt-1">
                    <ul class="nav nav-tabs" id="custom-tabs-two-tab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="equipment-tab" data-bs-toggle="tab" href="#equipment-content" role="tab" aria-controls="equipment-content" aria-selected="true">
                                <i class="fas fa-shield-alt mr-1"></i>装备管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="equipment-type-tab" data-bs-toggle="tab" href="#equipment-type-content" role="tab" aria-controls="equipment-type-content" aria-selected="false">
                                <i class="fas fa-tags mr-1"></i>装备类型管理
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="custom-tabs-two-tabContent">
                        <!-- 装备管理选项卡 -->
                        <div class="tab-pane fade show active" id="equipment-content" role="tabpanel" aria-labelledby="equipment-tab">
                            <!-- 搜索表单 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">搜索条件</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索条件 -->
                    <div class="search-form">
                        <div class="row mb-3">
                            <div class="col-lg-2 col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-hashtag text-primary mr-1"></i>装备ID
                                    </label>
                                    <div class="input-group">
                                        <input type="text" class="form-control form-control-search" placeholder="输入装备ID" v-model="queryForm.EquipId">
                                        <div class="input-group-append" v-if="queryForm.EquipId">
                                            <button class="btn btn-outline-secondary btn-sm" type="button" v-on:click="queryForm.EquipId = ''" title="清除">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-tag text-success mr-1"></i>装备名称
                                    </label>
                                    <div class="input-group">
                                        <input type="text" class="form-control form-control-search" placeholder="输入装备名称" v-model="queryForm.Name">
                                        <div class="input-group-append" v-if="queryForm.Name">
                                            <button class="btn btn-outline-secondary btn-sm" type="button" v-on:click="queryForm.Name = ''" title="清除">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-layer-group text-info mr-1"></i>装备类型
                                    </label>
                                    <select class="form-control form-control-search" v-model="queryForm.EquipTypeId">
                                        <option value="">全部类型</option>
                                        <option v-for="type in equipmentTypes" v-bind:key="type.equipTypeId" v-bind:value="type.equipTypeId">
                                            {{ type.typeName }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-yin-yang text-warning mr-1"></i>五行属性
                                    </label>
                                    <select class="form-control form-control-search" v-model="queryForm.Element">
                                        <option value="">全部五行</option>
                                        <option v-for="element in elements" v-bind:key="element" v-bind:value="element">
                                            {{ element }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-crown text-warning mr-1"></i>套装ID
                                    </label>
                                    <div class="input-group">
                                        <input type="text" class="form-control form-control-search" placeholder="输入套装ID" v-model="queryForm.SuitId">
                                        <div class="input-group-append" v-if="queryForm.SuitId">
                                            <button class="btn btn-outline-secondary btn-sm" type="button" v-on:click="queryForm.SuitId = ''" title="清除">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-12 col-sm-12">
                                <div class="form-group">
                                    <label class="form-label text-transparent">操作</label>
                                    <div class="search-buttons">
                                        <div class="btn-group d-flex" role="group">
                                            <button type="button" class="btn btn-primary btn-search" v-on:click="searchEquipments" title="搜索装备">
                                                <i class="fas fa-search"></i>
                                                <span class="d-none d-lg-inline ml-1">搜索</span>
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary btn-search" v-on:click="resetSearch" title="重置搜索条件">
                                                <i class="fas fa-undo"></i>
                                                <span class="d-none d-lg-inline ml-1">重置</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮区域 -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="search-stats text-muted">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        <span v-if="totalCount > 0">找到 {{ totalCount }} 条装备记录</span>
                                        <span v-else>暂无装备数据</span>
                                    </div>
                                    <div class="action-buttons">
                                        <button type="button" class="btn btn-success btn-shadow" v-on:click="showAddModal" title="新增装备">
                                            <i class="fas fa-plus mr-1"></i>新增装备
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 装备列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">装备列表</h3>
                </div>
                <div class="card-body">
                    <div v-if="loading" class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                    </div>
                    <div v-else>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th style="width: 100px;">装备ID</th>
                                        <th>装备名称</th>
                                        <th>装备类型</th>
                                        <th>五行属性</th>
                                        <th style="width: 80px;">攻击</th>
                                        <th style="width: 80px;">防御</th>
                                        <th style="width: 80px;">生命</th>
                                        <th style="width: 80px;">强化等级</th>
                                        <th style="width: 100px;">套装ID</th>
                                        <th style="width: 150px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="equipment in equipments" v-bind:key="equipment.equipId">
                                        <td>{{ equipment.equipId }}</td>
                                        <td>{{ equipment.name }}</td>
                                        <td>{{ equipment.equipTypeName }}</td>
                                        <td>{{ equipment.element || '无' }}</td>
                                        <td>{{ equipment.atk }}</td>
                                        <td>{{ equipment.def }}</td>
                                        <td>{{ equipment.hp }}</td>
                                        <td>{{ equipment.strengthenLevel }}</td>
                                        <td>{{ equipment.suitId || '无' }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-info btn-action" v-on:click="showDetailModal(equipment)" title="查看详情">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-warning btn-action" v-on:click="showEditModal(equipment)" title="编辑装备">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-action" v-on:click="deleteEquipment(equipment)" title="删除装备">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr v-if="equipments.length === 0">
                                        <td colspan="10" class="text-center">暂无数据</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="row mt-3" v-if="totalCount > 0">
                            <div class="col-sm-5">
                                <div class="dataTables_info">
                                    显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条
                                </div>
                            </div>
                            <div class="col-sm-7">
                                <div class="dataTables_paginate paging_simple_numbers float-right">
                                    <ul class="pagination">
                                        <li class="paginate_button page-item" v-bind:class="{ disabled: currentPage === 1 }">
                                            <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                        </li>
                                        <li class="paginate_button page-item" v-for="page in visiblePages" v-bind:key="page" v-bind:class="{ active: page === currentPage }">
                                            <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                                        </li>
                                        <li class="paginate_button page-item" v-bind:class="{ disabled: currentPage === totalPages }">
                                            <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                        </div>
                        
                        <!-- 装备类型管理选项卡 -->
                        <div class="tab-pane fade" id="equipment-type-content" role="tabpanel" aria-labelledby="equipment-type-tab">
                            <!-- 装备类型搜索表单 -->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">装备类型搜索</h3>
                                    <div class="card-tools">
                                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="search-form">
                                        <div class="row mb-3">
                                            <div class="col-lg-3 col-md-4 col-sm-6">
                                                <div class="form-group">
                                                    <label class="form-label">
                                                        <i class="fas fa-hashtag text-primary mr-1"></i>类型ID
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control form-control-search" placeholder="输入装备类型ID" v-model="typeQueryForm.EquipTypeId">
                                                        <div class="input-group-append" v-if="typeQueryForm.EquipTypeId">
                                                            <button class="btn btn-outline-secondary btn-sm" type="button" v-on:click="typeQueryForm.EquipTypeId = ''" title="清除">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-4 col-sm-6">
                                                <div class="form-group">
                                                    <label class="form-label">
                                                        <i class="fas fa-tag text-success mr-1"></i>类型名称
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control form-control-search" placeholder="输入装备类型名称" v-model="typeQueryForm.TypeName">
                                                        <div class="input-group-append" v-if="typeQueryForm.TypeName">
                                                            <button class="btn btn-outline-secondary btn-sm" type="button" v-on:click="typeQueryForm.TypeName = ''" title="清除">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-4 col-sm-12">
                                                <div class="form-group">
                                                    <label class="form-label text-transparent">操作</label>
                                                    <div class="search-buttons">
                                                        <div class="btn-group d-flex" role="group">
                                                            <button type="button" class="btn btn-primary btn-search" v-on:click="searchEquipmentTypes" title="搜索装备类型">
                                                                <i class="fas fa-search"></i>
                                                                <span class="d-none d-lg-inline ml-1">搜索</span>
                                                            </button>
                                                            <button type="button" class="btn btn-outline-secondary btn-search" v-on:click="resetTypeSearch" title="重置搜索条件">
                                                                <i class="fas fa-undo"></i>
                                                                <span class="d-none d-lg-inline ml-1">重置</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-12 col-sm-12">
                                                <div class="form-group">
                                                    <label class="form-label text-transparent">新增</label>
                                                    <div class="action-buttons">
                                                        <button type="button" class="btn btn-success btn-shadow" v-on:click="showAddTypeModal" title="新增装备类型">
                                                            <i class="fas fa-plus mr-1"></i>新增类型
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 统计信息 -->
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="search-stats text-muted">
                                                    <i class="fas fa-info-circle mr-1"></i>
                                                    <span v-if="typesTotalCount > 0">找到 {{ typesTotalCount }} 条装备类型记录</span>
                                                    <span v-else>暂无装备类型数据</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 装备类型列表 -->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">装备类型列表</h3>
                                </div>
                                <div class="card-body">
                                    <div v-if="typeLoading" class="text-center">
                                        <div class="spinner-border" role="status">
                                            <span class="sr-only">加载中...</span>
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 150px;">装备类型ID</th>
                                                        <th>装备类型名称</th>
                                                        <th style="width: 100px;">关联装备数</th>
                                                        <th style="width: 150px;">操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr v-for="equipType in equipmentTypesList" v-bind:key="equipType.equipTypeId">
                                                        <td>{{ equipType.equipTypeId }}</td>
                                                        <td>{{ equipType.typeName }}</td>
                                                        <td class="text-center">
                                                            <span class="badge-info">{{ equipType.equipmentCount || 0 }}</span>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group btn-group-sm" role="group">
                                                                <button type="button" class="btn btn-outline-warning btn-action" v-on:click="showEditTypeModal(equipType)" title="编辑装备类型">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-outline-danger btn-action" v-on:click="deleteEquipmentType(equipType)" title="删除装备类型" v-bind:disabled="equipType.equipmentCount > 0">
                                                                    <i class="fas fa-trash-alt"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr v-if="equipmentTypesList.length === 0">
                                                        <td colspan="4" class="text-center">暂无数据</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- 分页 -->
                                        <div class="row mt-3" v-if="typesTotalCount > 0">
                                            <div class="col-sm-5">
                                                <div class="dataTables_info">
                                                    显示第 {{ (typeCurrentPage - 1) * typePageSize + 1 }} 到 {{ Math.min(typeCurrentPage * typePageSize, typesTotalCount) }} 条记录，共 {{ typesTotalCount }} 条
                                                </div>
                                            </div>
                                            <div class="col-sm-7">
                                                <div class="dataTables_paginate paging_simple_numbers float-right">
                                                    <ul class="pagination">
                                                        <li class="paginate_button page-item" v-bind:class="{ disabled: typeCurrentPage === 1 }">
                                                            <a href="#" class="page-link" v-on:click.prevent="changeTypePage(typeCurrentPage - 1)">上一页</a>
                                                        </li>
                                                        <li class="paginate_button page-item" v-for="page in typeVisiblePages" v-bind:key="page" v-bind:class="{ active: page === typeCurrentPage }">
                                                            <a href="#" class="page-link" v-on:click.prevent="changeTypePage(page)">{{ page }}</a>
                                                        </li>
                                                        <li class="paginate_button page-item" v-bind:class="{ disabled: typeCurrentPage === typeTotalPages }">
                                                            <a href="#" class="page-link" v-on:click.prevent="changeTypePage(typeCurrentPage + 1)">下一页</a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新增/编辑装备模态框 -->
            <div class="modal fade" id="equipmentModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">{{ isEdit ? '编辑装备' : '新增装备' }}</h4>
                            <button type="button" class="close" v-on:click="closeModal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>装备ID <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" v-model="equipmentForm.equipId" v-bind:disabled="isEdit" placeholder="请输入装备ID">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>装备名称 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" v-model="equipmentForm.name" placeholder="请输入装备名称">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>装备类型 <span class="text-danger">*</span></label>
                                            <select class="form-control" v-model="equipmentForm.equipTypeId">
                                                <option value="">请选择装备类型</option>
                                                <option v-for="type in equipmentTypes" v-bind:key="type.equipTypeId" v-bind:value="type.equipTypeId">
                                                    {{ type.typeName }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>五行属性</label>
                                            <select class="form-control" v-model="equipmentForm.element">
                                                <option value="">请选择五行属性</option>
                                                <option v-for="element in elements" v-bind:key="element" v-bind:value="element">
                                                    {{ element }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>装备图标</label>
                                            <input type="text" class="form-control" v-model="equipmentForm.icon" placeholder="请输入装备图标路径">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>扩展槽位</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.slot" min="0" max="99">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>强化等级</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.strengthenLevel" min="0" max="99">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>套装ID</label>
                                            <input type="text" class="form-control" v-model="equipmentForm.suitId" placeholder="请输入套装ID">
                                        </div>
                                    </div>
                                </div>

                                <!-- 属性设置 -->
                                <h5 class="mt-3 mb-3">基础属性</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>攻击加成</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.atk" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>命中加成</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.hit" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>防御加成</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.def" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>速度加成</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.spd" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>闪避加成</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.dodge" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>生命加成</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.hp" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>魔法加成</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.mp" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>加深加成</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.deepen" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>抵消加成</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.offset" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>吸血加成</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.vamp" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>吸魔加成</label>
                                            <input type="number" class="form-control" v-model="equipmentForm.vampMp" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>主属性</label>
                                            <input type="text" class="form-control" v-model="equipmentForm.mainAttr" placeholder="主属性">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>主属性值</label>
                                            <input type="text" class="form-control" v-model="equipmentForm.mainAttrValue" placeholder="主属性值">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>五行限制</label>
                                            <select class="form-control" v-model="equipmentForm.elementLimit">
                                                <option value="">无限制</option>
                                                <option v-for="limit in elementLimits" v-bind:key="limit" v-bind:value="limit">
                                                    {{ limit }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>副属性</label>
                                            <input type="text" class="form-control" v-model="equipmentForm.subAttr" placeholder="副属性">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>副属性值</label>
                                            <input type="text" class="form-control" v-model="equipmentForm.subAttrValue" placeholder="副属性值">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>装备说明</label>
                                            <textarea class="form-control" v-model="equipmentForm.description" rows="3" placeholder="装备说明"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>装备名称（详情表）</label>
                                            <input type="text" class="form-control" v-model="equipmentForm.equipName" placeholder="装备名称（详情表）">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary btn-shadow" v-on:click="closeModal">
                                <i class="fas fa-times mr-1"></i>取消
                            </button>
                            <button type="button" class="btn btn-primary btn-shadow" v-on:click="saveEquipment" v-bind:disabled="saving">
                                <span v-if="saving" class="spinner-border spinner-border-sm mr-1"></span>
                                <i v-else class="fas fa-save mr-1"></i>
                                {{ isEdit ? '更新装备' : '创建装备' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新增/编辑装备类型模态框 -->
            <div class="modal fade" id="equipmentTypeModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">{{ isTypeEdit ? '编辑装备类型' : '新增装备类型' }}</h4>
                            <button type="button" class="close" v-on:click="closeTypeModal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form>
                                <div class="form-group">
                                    <label>装备类型ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" v-model="equipmentTypeForm.equipTypeId" v-bind:disabled="isTypeEdit" placeholder="请输入装备类型ID（如：weapon, armor等）">
                                    <small class="form-text text-muted">装备类型ID用于程序识别，创建后不可修改</small>
                                </div>
                                <div class="form-group">
                                    <label>装备类型名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" v-model="equipmentTypeForm.typeName" placeholder="请输入装备类型名称（如：武器、护甲等）">
                                    <small class="form-text text-muted">装备类型名称用于前端显示</small>
                                </div>
                                <div class="form-group">
                                    <label>类型描述</label>
                                    <textarea class="form-control" v-model="equipmentTypeForm.description" rows="3" placeholder="请输入类型描述"></textarea>
                                    <small class="form-text text-muted">装备类型的详细描述</small>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>排序号</label>
                                            <input type="number" class="form-control" v-model="equipmentTypeForm.sortOrder" min="0" max="9999" placeholder="排序号">
                                            <small class="form-text text-muted">数字越小排序越靠前</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>是否启用</label>
                                            <select class="form-control" v-model="equipmentTypeForm.isActive">
                                                <option value="true">启用</option>
                                                <option value="false">禁用</option>
                                            </select>
                                            <small class="form-text text-muted">禁用后该类型将不可用</small>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary btn-shadow" v-on:click="closeTypeModal">
                                <i class="fas fa-times mr-1"></i>取消
                            </button>
                            <button type="button" class="btn btn-primary btn-shadow" v-on:click="saveEquipmentType" v-bind:disabled="typeSaving">
                                <span v-if="typeSaving" class="spinner-border spinner-border-sm mr-1"></span>
                                <i v-else class="fas fa-save mr-1"></i>
                                {{ isTypeEdit ? '更新类型' : '创建类型' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 装备详情模态框 -->
            <div class="modal fade" id="detailModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">装备详情</h4>
                            <button type="button" class="close" v-on:click="closeDetailModal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body" v-if="currentEquipment">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td width="120"><strong>装备ID：</strong></td>
                                            <td>{{ currentEquipment.equipId }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>装备类ID：</strong></td>
                                            <td>{{ currentEquipment.classId }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>装备名称：</strong></td>
                                            <td>{{ currentEquipment.name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>装备类型：</strong></td>
                                            <td>{{ currentEquipment.equipTypeName }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>五行属性：</strong></td>
                                            <td>{{ currentEquipment.element || '无' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>扩展槽位：</strong></td>
                                            <td>{{ currentEquipment.slot }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>强化等级：</strong></td>
                                            <td>{{ currentEquipment.strengthenLevel }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>套装ID：</strong></td>
                                            <td>{{ currentEquipment.suitId || '无' }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>属性加成</h6>
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td width="80">攻击：</td>
                                            <td>{{ currentEquipment.atk }}</td>
                                            <td width="80">命中：</td>
                                            <td>{{ currentEquipment.hit }}</td>
                                        </tr>
                                        <tr>
                                            <td>防御：</td>
                                            <td>{{ currentEquipment.def }}</td>
                                            <td>速度：</td>
                                            <td>{{ currentEquipment.spd }}</td>
                                        </tr>
                                        <tr>
                                            <td>闪避：</td>
                                            <td>{{ currentEquipment.dodge }}</td>
                                            <td>生命：</td>
                                            <td>{{ currentEquipment.hp }}</td>
                                        </tr>
                                        <tr>
                                            <td>魔法：</td>
                                            <td>{{ currentEquipment.mp }}</td>
                                            <td>加深：</td>
                                            <td>{{ currentEquipment.deepen }}</td>
                                        </tr>
                                        <tr>
                                            <td>抵消：</td>
                                            <td>{{ currentEquipment.offset }}</td>
                                            <td>吸血：</td>
                                            <td>{{ currentEquipment.vamp }}</td>
                                        </tr>
                                        <tr>
                                            <td>吸魔：</td>
                                            <td>{{ currentEquipment.vampMp }}</td>
                                            <td>主属性：</td>
                                            <td>{{ currentEquipment.mainAttr || '无' }}</td>
                                        </tr>
                                        <tr>
                                            <td>主属性值：</td>
                                            <td>{{ currentEquipment.mainAttrValue || '无' }}</td>
                                            <td>副属性：</td>
                                            <td>{{ currentEquipment.subAttr || '无' }}</td>
                                        </tr>
                                        <tr>
                                            <td>副属性值：</td>
                                            <td>{{ currentEquipment.subAttrValue || '无' }}</td>
                                            <td>五行限制：</td>
                                            <td>{{ currentEquipment.elementLimit || '无' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <div class="row" v-if="currentEquipment.description">
                                <div class="col-md-12">
                                    <h6>装备说明</h6>
                                    <p class="text-muted">{{ currentEquipment.description }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary btn-shadow" v-on:click="closeDetailModal">
                                <i class="fas fa-times mr-1"></i>关闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script src="~/lib/vue/vue.global.js"></script>
<script src="~/lib/axios.min.js"></script>
<script>
    const { createApp, ref, computed, onMounted } = Vue;

    const equipmentApp = createApp({
        setup() {
            // 响应式数据
            const loading = ref(false);
            const saving = ref(false);
            const equipments = ref([]);
            const equipmentTypes = ref(@Html.Raw(Json.Serialize(ViewBag.EquipmentTypes ?? new List<object>())));
            const elements = ref(@Html.Raw(Json.Serialize(ViewBag.Elements ?? new List<string>())));
            const elementLimits = ref(@Html.Raw(Json.Serialize(ViewBag.ElementLimits ?? new List<string>())));
            const totalCount = ref(0);
            const currentPage = ref(1);
            const pageSize = ref(10);
            
            const queryForm = ref({
                EquipId: '',
                Name: '',
                EquipTypeId: '',
                Element: '',
                SuitId: '',
                MainAttr: '',
                SubAttr: '',
                Page: 1,
                PageSize: 10
            });
            
            const equipmentForm = ref({
                equipId: '',
                classId: '',
                name: '',
                icon: '',
                equipTypeId: '',
                element: '',
                slot: 0,
                strengthenLevel: 0,
                suitId: '',
                atk: 0,
                hit: 0,
                def: 0,
                spd: 0,
                dodge: 0,
                hp: 0,
                mp: 0,
                deepen: 0,
                offset: 0,
                vamp: 0,
                vampMp: 0,
                description: '',
                mainAttr: '',
                mainAttrValue: '',
                subAttr: '',
                subAttrValue: '',
                elementLimit: '',
                equipName: ''
            });
            
            const currentEquipment = ref(null);
            const isEdit = ref(false);

            // ==================== 装备类型管理数据 ====================
            const typeLoading = ref(false);
            const typeSaving = ref(false);
            const equipmentTypesList = ref([]);
            const typesTotalCount = ref(0);
            const typeCurrentPage = ref(1);
            const typePageSize = ref(10);
            
            const typeQueryForm = ref({
                EquipTypeId: '',
                TypeName: '',
                Page: 1,
                PageSize: 10
            });
            
            const equipmentTypeForm = ref({
                equipTypeId: '',
                typeName: '',
                description: '',
                sortOrder: 0,
                isActive: true
            });
            
            const isTypeEdit = ref(false);

            // 计算属性
            const totalPages = computed(() => {
                return Math.ceil(totalCount.value / pageSize.value);
            });

            const visiblePages = computed(() => {
                const start = Math.max(1, currentPage.value - 2);
                const end = Math.min(totalPages.value, currentPage.value + 2);
                const pages = [];
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            });

            // 装备类型计算属性
            const typeTotalPages = computed(() => {
                return Math.ceil(typesTotalCount.value / typePageSize.value);
            });

            const typeVisiblePages = computed(() => {
                const start = Math.max(1, typeCurrentPage.value - 2);
                const end = Math.min(typeTotalPages.value, typeCurrentPage.value + 2);
                const pages = [];
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            });

            // 方法
            const loadEquipments = async () => {
                loading.value = true;
                try {
                    const requestData = {
                        ...queryForm.value,
                        Page: currentPage.value,
                        PageSize: pageSize.value
                    };
                    console.log('发送的请求参数：', requestData);
                    
                    const response = await axios.post('/Equipment/GetList', requestData);

                    if (response.data.code === 200) {
                        equipments.value = response.data.data || [];
                        totalCount.value = response.data.total || 0;
                    } else {
                        alert('加载装备列表失败');
                    }
                } catch (error) {
                    console.error('加载装备列表出错：', error);
                    alert('加载装备列表失败');
                } finally {
                    loading.value = false;
                }
            };

            const searchEquipments = () => {
                currentPage.value = 1;
                loadEquipments();
            };

            const resetSearch = () => {
                queryForm.value = {
                    EquipId: '',
                    Name: '',
                    EquipTypeId: '',
                    Element: '',
                    SuitId: '',
                    MainAttr: '',
                    SubAttr: '',
                    Page: 1,
                    PageSize: 10
                };
                currentPage.value = 1;
                loadEquipments();
            };

            const resetForm = () => {
                equipmentForm.value = {
                    equipId: '',
                    classId: '',
                    name: '',
                    icon: '',
                    equipTypeId: '',
                    element: '',
                    slot: 0,
                    strengthenLevel: 0,
                    suitId: '',
                    atk: 0,
                    hit: 0,
                    def: 0,
                    spd: 0,
                    dodge: 0,
                    hp: 0,
                    mp: 0,
                    deepen: 0,
                    offset: 0,
                    vamp: 0,
                    vampMp: 0,
                    description: '',
                    mainAttr: '',
                    mainAttrValue: '',
                    subAttr: '',
                    subAttrValue: '',
                    elementLimit: '',
                    equipName: ''
                };
            };

            const showAddModal = () => {
                isEdit.value = false;
                resetForm();
                $('#equipmentModal').modal('show');
            };

            const showEditModal = async (equipment) => {
                isEdit.value = true;
                
                // 获取完整的装备信息
                try {
                    const response = await axios.get(`/Equipment/GetById?equipId=${equipment.equipId}`);
                    if (response.data.code === 200) {
                        const data = response.data.data;
                        equipmentForm.value = {
                            equipId: data.equipId,
                            classId: data.classId,
                            name: data.name,
                            icon: data.icon || '',
                            equipTypeId: data.equipTypeId,
                            element: data.element || '',
                            slot: data.slot,
                            strengthenLevel: data.strengthenLevel,
                            suitId: data.suitId || '',
                            atk: data.atk,
                            hit: data.hit,
                            def: data.def,
                            spd: data.spd,
                            dodge: data.dodge,
                            hp: data.hp,
                            mp: data.mp,
                            deepen: data.deepen,
                            offset: data.offset,
                            vamp: data.vamp,
                            vampMp: data.vampMp,
                            description: data.description || '',
                            mainAttr: data.mainAttr || '',
                            mainAttrValue: data.mainAttrValue || '',
                            subAttr: data.subAttr || '',
                            subAttrValue: data.subAttrValue || '',
                            elementLimit: data.elementLimit || '',
                            equipName: data.equipName || ''
                        };
                        $('#equipmentModal').modal('show');
                    } else {
                        alert('获取装备信息失败');
                    }
                } catch (error) {
                    console.error('获取装备信息出错：', error);
                    alert('获取装备信息失败');
                }
            };

            const showDetailModal = (equipment) => {
                currentEquipment.value = equipment;
                $('#detailModal').modal('show');
            };

            const saveEquipment = async () => {
                // 验证表单
                if (!equipmentForm.value.equipId || !equipmentForm.value.name || !equipmentForm.value.equipTypeId) {
                    alert('请填写必填字段');
                    return;
                }

                // 自动设置classId为equipTypeId（后端需要但前端不显示）
                if (!equipmentForm.value.classId) {
                    equipmentForm.value.classId = equipmentForm.value.equipTypeId;
                }

                saving.value = true;
                try {
                    const url = isEdit.value ? '/Equipment/Update' : '/Equipment/Create';
                    const response = await axios.post(url, equipmentForm.value);

                    if (response.data.code === 200) {
                        alert(isEdit.value ? '装备更新成功' : '装备创建成功');
                        $('#equipmentModal').modal('hide');
                        loadEquipments();
                    } else {
                        alert(response.data.message || '操作失败');
                    }
                } catch (error) {
                    console.error('保存装备出错：', error);
                    alert('保存装备失败');
                } finally {
                    saving.value = false;
                }
            };

            const deleteEquipment = async (equipment) => {
                if (!confirm(`确定要删除装备"${equipment.name}"吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/Equipment/Delete', {
                        equipId: equipment.equipId
                    });

                    if (response.data.code === 200) {
                        alert('删除成功');
                        loadEquipments();
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除装备出错：', error);
                    alert('删除失败');
                }
            };

            const changePage = (page) => {
                if (page < 1 || page > totalPages.value || page === currentPage.value) {
                    return;
                }
                currentPage.value = page;
                loadEquipments();
            };

            // 关闭模态框
            const closeModal = () => {
                $('#equipmentModal').modal('hide');
            };

            // 关闭详情模态框
            const closeDetailModal = () => {
                $('#detailModal').modal('hide');
            };

            // ==================== 装备类型管理方法 ====================

            // 加载装备类型列表
            const loadEquipmentTypes = async () => {
                typeLoading.value = true;
                try {
                    const requestData = {
                        ...typeQueryForm.value,
                        Page: typeCurrentPage.value,
                        PageSize: typePageSize.value
                    };
                    console.log('发送的装备类型查询参数：', requestData);
                    
                    const response = await axios.post('/Equipment/GetEquipmentTypeList', requestData);

                    if (response.data.code === 200) {
                        equipmentTypesList.value = response.data.data || [];
                        typesTotalCount.value = response.data.total || 0;
                    } else {
                        alert('加载装备类型列表失败');
                    }
                } catch (error) {
                    console.error('加载装备类型列表出错：', error);
                    alert('加载装备类型列表失败');
                } finally {
                    typeLoading.value = false;
                }
            };

            // 搜索装备类型
            const searchEquipmentTypes = () => {
                typeCurrentPage.value = 1;
                loadEquipmentTypes();
            };

            // 重置装备类型搜索
            const resetTypeSearch = () => {
                typeQueryForm.value = {
                    EquipTypeId: '',
                    TypeName: '',
                    Page: 1,
                    PageSize: 10
                };
                typeCurrentPage.value = 1;
                loadEquipmentTypes();
            };

            // 重置装备类型表单
            const resetTypeForm = () => {
                equipmentTypeForm.value = {
                    equipTypeId: '',
                    typeName: '',
                    description: '',
                    sortOrder: 0,
                    isActive: true
                };
            };

            // 显示新增装备类型模态框
            const showAddTypeModal = () => {
                isTypeEdit.value = false;
                resetTypeForm();
                $('#equipmentTypeModal').modal('show');
            };

            // 显示编辑装备类型模态框
            const showEditTypeModal = async (equipmentType) => {
                isTypeEdit.value = true;
                
                try {
                    const response = await axios.get(`/Equipment/GetEquipmentTypeById?equipTypeId=${equipmentType.equipTypeId}`);
                    if (response.data.code === 200) {
                        const data = response.data.data;
                        equipmentTypeForm.value = {
                            equipTypeId: data.equipTypeId,
                            typeName: data.typeName,
                            description: data.description || '',
                            sortOrder: data.sortOrder || 0,
                            isActive: data.isActive !== undefined ? data.isActive : true
                        };
                        $('#equipmentTypeModal').modal('show');
                    } else {
                        alert('获取装备类型信息失败');
                    }
                } catch (error) {
                    console.error('获取装备类型信息出错：', error);
                    alert('获取装备类型信息失败');
                }
            };

            // 保存装备类型
            const saveEquipmentType = async () => {
                // 验证表单
                if (!equipmentTypeForm.value.equipTypeId || !equipmentTypeForm.value.typeName) {
                    alert('请填写必填字段');
                    return;
                }

                typeSaving.value = true;
                try {
                    const url = isTypeEdit.value ? '/Equipment/UpdateEquipmentType' : '/Equipment/CreateEquipmentType';
                    const response = await axios.post(url, equipmentTypeForm.value);

                    if (response.data.code === 200) {
                        alert(isTypeEdit.value ? '装备类型更新成功' : '装备类型创建成功');
                        $('#equipmentTypeModal').modal('hide');
                        loadEquipmentTypes();
                        
                        // 刷新装备管理页面的装备类型下拉选项
                        try {
                            const typesResponse = await axios.get('/Equipment/GetEquipmentTypes');
                            if (typesResponse.data && Array.isArray(typesResponse.data)) {
                                equipmentTypes.value = typesResponse.data;
                            }
                        } catch (error) {
                            console.log('刷新装备类型下拉选项失败：', error);
                        }
                    } else {
                        alert(response.data.message || '操作失败');
                    }
                } catch (error) {
                    console.error('保存装备类型出错：', error);
                    alert('保存装备类型失败');
                } finally {
                    typeSaving.value = false;
                }
            };

            // 删除装备类型
            const deleteEquipmentType = async (equipmentType) => {
                if (equipmentType.equipmentCount > 0) {
                    alert(`该装备类型下还有 ${equipmentType.equipmentCount} 个装备，无法删除！`);
                    return;
                }

                if (!confirm(`确定要删除装备类型"${equipmentType.typeName}"吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/Equipment/DeleteEquipmentType', {
                        equipTypeId: equipmentType.equipTypeId
                    });

                    if (response.data.code === 200) {
                        alert('删除成功');
                        loadEquipmentTypes();
                        
                        // 刷新装备管理页面的装备类型下拉选项
                        try {
                            const typesResponse = await axios.get('/Equipment/GetEquipmentTypes');
                            if (typesResponse.data && Array.isArray(typesResponse.data)) {
                                equipmentTypes.value = typesResponse.data;
                            }
                        } catch (error) {
                            console.log('刷新装备类型下拉选项失败：', error);
                        }
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除装备类型出错：', error);
                    alert('删除失败');
                }
            };

            // 装备类型分页
            const changeTypePage = (page) => {
                if (page < 1 || page > typeTotalPages.value || page === typeCurrentPage.value) {
                    return;
                }
                typeCurrentPage.value = page;
                loadEquipmentTypes();
            };

            // 关闭装备类型模态框
            const closeTypeModal = () => {
                $('#equipmentTypeModal').modal('hide');
            };

            // 生命周期
            onMounted(async () => {
                // 顺序执行，避免DataReader冲突
                await loadEquipments();
                await loadEquipmentTypes();
            });

            return {
                // 装备管理相关
                loading,
                saving,
                equipments,
                equipmentTypes,
                elements,
                elementLimits,
                totalCount,
                currentPage,
                pageSize,
                queryForm,
                equipmentForm,
                currentEquipment,
                isEdit,
                totalPages,
                visiblePages,
                loadEquipments,
                searchEquipments,
                resetSearch,
                showAddModal,
                showEditModal,
                showDetailModal,
                saveEquipment,
                deleteEquipment,
                changePage,
                closeModal,
                closeDetailModal,
                
                // 装备类型管理相关
                typeLoading,
                typeSaving,
                equipmentTypesList,
                typesTotalCount,
                typeCurrentPage,
                typePageSize,
                typeQueryForm,
                equipmentTypeForm,
                isTypeEdit,
                typeTotalPages,
                typeVisiblePages,
                loadEquipmentTypes,
                searchEquipmentTypes,
                resetTypeSearch,
                showAddTypeModal,
                showEditTypeModal,
                saveEquipmentType,
                deleteEquipmentType,
                changeTypePage,
                closeTypeModal
            };
        }
    });

    equipmentApp.mount('#equipmentApp');
</script> 