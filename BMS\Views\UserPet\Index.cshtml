@{
    ViewData["Title"] = "用户宠物管理";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    /* 强制应用科幻主题 */
    #userPetApp .cyber-card {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #e2e8f0 !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
        margin-bottom: 1.5rem !important;
    }

    #userPetApp .cyber-card-header {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1)) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
        padding: 1rem 1.5rem !important;
        border-radius: 12px 12px 0 0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 0.75rem !important;
    }

    #userPetApp .cyber-card-body {
        padding: 1.5rem !important;
    }

    #userPetApp .cyber-form-control {
        background: rgba(26, 26, 46, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: #e2e8f0 !important;
        border-radius: 8px !important;
        padding: 0.75rem 1rem !important;
        transition: all 0.3s ease !important;
    }

    #userPetApp .cyber-form-control:focus {
        border-color: #00d4ff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25) !important;
        background: rgba(26, 26, 46, 0.9) !important;
    }

    #userPetApp .cyber-btn {
        background: linear-gradient(135deg, #00d4ff, #8b5cf6) !important;
        border: none !important;
        color: white !important;
        padding: 0.75rem 1.5rem !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
    }

    #userPetApp .cyber-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3) !important;
    }

    #userPetApp .cyber-btn-info {
        background: linear-gradient(135deg, #17a2b8, #20c997) !important;
    }

    #userPetApp .cyber-btn-info:hover {
        box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3) !important;
    }

    #userPetApp .cyber-btn-warning {
        background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
    }

    #userPetApp .cyber-btn-warning:hover {
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3) !important;
    }

    #userPetApp .cyber-btn-danger {
        background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
    }

    #userPetApp .cyber-btn-danger:hover {
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3) !important;
    }

    #userPetApp .cyber-table {
        background: transparent !important;
        color: #e2e8f0 !important;
        border-radius: 8px !important;
        overflow: hidden !important;
    }

    #userPetApp .cyber-table th {
        background: linear-gradient(135deg, #1a1a2e, #16213e) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
        color: #e2e8f0 !important;
        padding: 1rem !important;
        font-weight: 600 !important;
    }

    #userPetApp .cyber-table td {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #e2e8f0 !important;
        padding: 0.75rem 1rem !important;
    }

    #userPetApp .cyber-table tbody tr:hover {
        background: rgba(0, 212, 255, 0.1) !important;
    }

    /* 科幻模态框样式 */
    .cyber-modal .modal-content {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #e2e8f0 !important;
        border-radius: 12px !important;
    }

    .cyber-modal .modal-header {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1)) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
        color: #e2e8f0 !important;
    }

    .cyber-modal .modal-body {
        background: transparent !important;
        color: #e2e8f0 !important;
    }

    .cyber-modal .modal-footer {
        background: transparent !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    /* 可搜索下拉框样式 */
    .searchable-select-container {
        position: relative;
    }

    .searchable-select-input {
        padding-right: 40px !important;
    }

    .searchable-select-arrow {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #00d4ff;
        cursor: pointer;
        transition: transform 0.3s ease;
        z-index: 10;
    }

    .searchable-select-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        max-height: 200px;
        overflow-y: auto;
        background: rgba(26, 26, 46, 0.95);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: 8px;
        margin-top: 4px;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .searchable-select-option {
        padding: 12px 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: #e2e8f0;
    }

    .searchable-select-option:last-child {
        border-bottom: none;
    }

    .searchable-select-option:hover {
        background: rgba(0, 212, 255, 0.1) !important;
    }

    .searchable-select-option.selected {
        background: rgba(0, 212, 255, 0.2) !important;
    }

    .searchable-select-option.no-results {
        color: #94a3b8;
        text-align: center;
        font-style: italic;
        cursor: default;
    }

    .searchable-select-option.no-results:hover {
        background: transparent !important;
    }

    /* 滚动条样式 */
    .searchable-select-dropdown::-webkit-scrollbar {
        width: 6px;
    }

    .searchable-select-dropdown::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
    }

    .searchable-select-dropdown::-webkit-scrollbar-thumb {
        background: rgba(0, 212, 255, 0.5);
        border-radius: 3px;
    }

    .searchable-select-dropdown::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 212, 255, 0.7);
    }
</style>

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<div class="container-fluid p-4">
    <div id="userPetApp">
        <!-- 科幻页面标题 -->
        <div class="cyber-card mb-4">
            <div class="cyber-card-header">
                <div class="cyber-icon">
                    <i class="fas fa-paw"></i>
                </div>
                <h1 class="cyber-card-title">用户宠物管理</h1>
            </div>
        </div>

        <!-- 科幻搜索条件 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h5 class="cyber-card-title">搜索条件</h5>
            </div>
            <div class="cyber-card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">用户ID</label>
                            <input type="number" v-model.number="queryForm.userId" class="cyber-form-control" placeholder="请输入用户ID">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">用户名</label>
                            <input type="text" v-model="queryForm.userName" class="cyber-form-control" placeholder="请输入用户名">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">宠物序号</label>
                            <input type="number" v-model.number="queryForm.petNo" class="cyber-form-control" placeholder="请输入宠物序号">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">宠物名称</label>
                            <input type="text" v-model="queryForm.petName" class="cyber-form-control" placeholder="请输入宠物名称">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">宠物属性</label>
                            <select v-model="queryForm.petAttribute" class="cyber-form-control">
                                <option value="">全部</option>
                                <option value="火">火</option>
                                <option value="水">水</option>
                                <option value="木">木</option>
                                <option value="金">金</option>
                                <option value="土">土</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">宠物状态</label>
                            <select v-model="queryForm.status" class="cyber-form-control">
                                <option value="">全部状态</option>
                                <option value="牧场">牧场</option>
                                <option value="携带">携带</option>
                                <option value="丢弃">丢弃</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="button" class="cyber-btn" v-on:click="searchUserPets" v-bind:disabled="loading">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetQuery">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showAddModal">
                                    <i class="fas fa-plus"></i> 新增宠物
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻数据列表 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">
                    <i class="fas fa-list"></i>
                </div>
                <h5 class="cyber-card-title">用户宠物列表</h5>
            </div>
            <div class="cyber-card-body">
                <div class="cyber-table-container">
                    <table class="cyber-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户</th>
                                <th>宠物编号</th>
                                <th>宠物名称</th>
                                <th>属性</th>
                                <th>等级</th>
                                <th>生命值</th>
                                <th>攻击力</th>
                                <th>防御力</th>
                                <th>状态</th>
                                <th>主战</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="loading">
                                <td colspan="12" class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </td>
                            </tr>
                            <tr v-else-if="userPetsList.length === 0">
                                <td colspan="12" class="text-center">暂无数据</td>
                            </tr>
                            <tr v-else v-for="userPet in userPetsList" v-bind:key="userPet.id">
                                <td>{{ userPet.id }}</td>
                                <td>{{ userPet.userName }}</td>
                                <td>{{ userPet.petNo }}</td>
                                <td>{{ userPet.petName }}</td>
                                <td>{{ userPet.petAttribute }}</td>
                                <td>{{ calculateLevel(userPet.exp) }}</td>
                                <td>{{ userPet.hp }}</td>
                                <td>{{ userPet.atk }}</td>
                                <td>{{ userPet.def }}</td>
                                <td>
                                    <span class="badge" v-bind:class="{
                                        'badge-success': userPet.status === '携带',
                                        'badge-secondary': userPet.status === '牧场',
                                        'badge-danger': userPet.status === '丢弃'
                                    }">
                                        {{ userPet.status || '牧场' }}
                                    </span>
                                </td>
                                <td>
                                    <span v-if="userPet.IsMain || userPet.isMain" class="badge badge-warning">
                                        <i class="fas fa-star"></i> 主战
                                    </span>
                                    <span v-else class="text-muted">-</span>
                                </td>
                                <td>
                                    <div class="d-flex gap-1 flex-wrap">
                                        <button type="button" class="cyber-btn cyber-btn-sm" v-on:click="viewDetail(userPet)">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-warning cyber-btn-sm" v-on:click="editUserPet(userPet)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-info cyber-btn-sm" v-on:click="copyUserPet(userPet)">
                                            <i class="fas fa-copy"></i> 复制
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-danger cyber-btn-sm" v-on:click="deleteUserPet(userPet)">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 科幻分页 -->
                <div v-if="totalCount > 0" class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条，共 {{ totalCount }} 条记录
                    </div>
                    <nav>
                        <ul class="cyber-pagination pagination">
                            <li class="page-item" v-bind:class="{ disabled: currentPage === 1 }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                            </li>
                            <li class="page-item"
                                v-for="page in visiblePages"
                                v-bind:key="page"
                                v-bind:class="{ active: page === currentPage }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                            </li>
                            <li class="page-item" v-bind:class="{ disabled: currentPage === totalPages }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 科幻新增/编辑用户宠物模态框 -->
        <div class="modal fade cyber-modal" id="userPetModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">
                            <i class="fas fa-paw me-2"></i>{{ isEdit ? '编辑用户宠物' : '新增用户宠物' }}
                        </h4>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" v-on:click="closeModal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="row" v-if="!isEdit">
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">用户 <span class="text-danger">*</span></label>
                                        <select v-model="userPetForm.userId" class="cyber-form-control" required>
                                            <option value="">请选择用户</option>
                                            <option v-for="user in userOptions" v-bind:key="user.value" v-bind:value="user.value">
                                                {{ user.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">宠物配置 <span class="text-danger">*</span></label>
                                        <div class="searchable-select-container" style="position: relative;">
                                            <input
                                                type="text"
                                                v-model="petSearchText"
                                                v-on:focus="showPetDropdown = true"
                                                v-on:blur="hidePetDropdown"
                                                v-on:input="filterPetOptions"
                                                class="cyber-form-control searchable-select-input"
                                                placeholder="搜索宠物名称或属性..."
                                                autocomplete="off"
                                                required>
                                            <i class="fas fa-chevron-down searchable-select-arrow"
                                               v-on:click="togglePetDropdown"
                                               style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #00d4ff; cursor: pointer; transition: transform 0.3s ease;"
                                               v-bind:style="{ transform: showPetDropdown ? 'translateY(-50%) rotate(180deg)' : 'translateY(-50%)' }"></i>
                                            <div v-show="showPetDropdown"
                                                 class="searchable-select-dropdown"
                                                 style="position: absolute; top: 100%; left: 0; right: 0; z-index: 1000; max-height: 200px; overflow-y: auto; background: rgba(26, 26, 46, 0.95); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: 8px; margin-top: 4px; backdrop-filter: blur(10px);">
                                                <div v-if="filteredPetOptions.length === 0"
                                                     class="searchable-select-option no-results"
                                                     style="padding: 12px 16px; color: #94a3b8; text-align: center; font-style: italic;">
                                                    没有找到匹配的宠物
                                                </div>
                                                <div v-for="pet in filteredPetOptions"
                                                     v-bind:key="pet.petNo"
                                                     v-on:mousedown="selectPet(pet)"
                                                     class="searchable-select-option"
                                                     v-bind:class="{ 'selected': userPetForm.petNo === pet.petNo }"
                                                     style="padding: 12px 16px; cursor: pointer; transition: all 0.3s ease; border-bottom: 1px solid rgba(255, 255, 255, 0.1);"
                                                     v-on:mouseover="$event.target.style.background = 'rgba(0, 212, 255, 0.1)'"
                                                     v-on:mouseout="$event.target.style.background = userPetForm.petNo === pet.petNo ? 'rgba(0, 212, 255, 0.2)' : 'transparent'">
                                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                                        <span style="color: #e2e8f0; font-weight: 500;">{{ pet.name }}</span>
                                                        <span style="color: #00d4ff; font-size: 0.85em; padding: 2px 8px; background: rgba(0, 212, 255, 0.1); border-radius: 12px;">{{ pet.attribute }}</span>
                                                    </div>
                                                    <div v-if="pet.description" style="color: #94a3b8; font-size: 0.8em; margin-top: 4px;">{{ pet.description }}</div>
                                                </div>
                                            </div>
                                            <input type="hidden" v-model="userPetForm.petNo" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">经验值</label>
                                        <input type="number" v-model.number="userPetForm.exp" class="cyber-form-control" placeholder="请输入经验值">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">生命值</label>
                                        <input type="number" v-model.number="userPetForm.hp" class="cyber-form-control" placeholder="请输入生命值">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">攻击力</label>
                                        <input type="number" v-model.number="userPetForm.atk" class="cyber-form-control" placeholder="请输入攻击力">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">防御力</label>
                                        <input type="number" v-model.number="userPetForm.def" class="cyber-form-control" placeholder="请输入防御力">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">速度</label>
                                        <input type="number" v-model.number="userPetForm.spd" class="cyber-form-control" placeholder="请输入速度">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">宠物状态 <span class="text-danger">*</span></label>
                                        <select v-model="userPetForm.status" class="cyber-form-control" required>
                                            <option value="">请选择状态</option>
                                            <option value="牧场">牧场</option>
                                            <option value="携带">携带</option>
                                            <option value="丢弃">丢弃</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">&nbsp;</label>
                                        <div class="form-check d-block">
                                            <input
                                                type="checkbox"
                                                :checked="userPetForm.isMain"
                                                class="cyber-checkbox"
                                                id="isMainCheck"
                                                v-on:change="handleMainPetChange">
                                            <label class="form-check-label text-light" for="isMainCheck">
                                                设置为主战宠物
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeModal">取消</button>
                        <button type="button" class="cyber-btn" v-on:click="saveUserPet" v-bind:disabled="saving">
                            <i v-if="saving" class="fas fa-spinner fa-spin"></i>
                            {{ saving ? '保存中...' : '保存' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻宠物详情模态框 -->
        <div class="modal fade cyber-modal" id="detailModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">
                            <i class="fas fa-info-circle me-2"></i>宠物详情
                        </h4>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" v-on:click="closeDetailModal"></button>
                    </div>
                    <div class="modal-body" v-if="selectedUserPet">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-table-container">
                                    <table class="cyber-table">
                                        <tr><td>用户名</td><td>{{ selectedUserPet.userName }}</td></tr>
                                        <tr><td>宠物编号</td><td>{{ selectedUserPet.petNo }}</td></tr>
                                        <tr><td>宠物名称</td><td>{{ selectedUserPet.petName }}</td></tr>
                                        <tr><td>宠物属性</td><td>{{ selectedUserPet.petAttribute }}</td></tr>
                                        <tr><td>等级</td><td>{{ calculateLevel(selectedUserPet.exp) }}</td></tr>
                                        <tr><td>经验值</td><td>{{ selectedUserPet.exp }}</td></tr>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-table-container">
                                    <table class="cyber-table">
                                        <tr><td>生命值</td><td>{{ selectedUserPet.hp }}</td></tr>
                                        <tr><td>攻击力</td><td>{{ selectedUserPet.atk }}</td></tr>
                                        <tr><td>防御力</td><td>{{ selectedUserPet.def }}</td></tr>
                                        <tr><td>速度</td><td>{{ selectedUserPet.spd }}</td></tr>
                                        <tr><td>状态</td><td>{{ selectedUserPet.status || '牧场' }}</td></tr>
                                        <tr><td>是否主战</td><td>{{ (selectedUserPet.IsMain || selectedUserPet.isMain) ? '是' : '否' }}</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeDetailModal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const { createApp, ref, computed, onMounted, nextTick, watch } = Vue;

    createApp({
        setup() {
            // 响应式数据
            const loading = ref(false);
            const saving = ref(false);
            const userPetsList = ref([]);
            const totalCount = ref(0);
            const currentPage = ref(1);
            const pageSize = ref(10);
            const isEdit = ref(false);
            const selectedUserPet = ref(null);

            // 下拉选项
            const userOptions = ref([]);
            const petConfigOptions = ref([]);

            // 宠物搜索相关
            const petSearchText = ref('');
            const showPetDropdown = ref(false);
            const filteredPetOptions = ref([]);

            // 查询表单
            const queryForm = ref({
                userId: null,
                userName: '',
                petNo: null,
                petName: '',
                petAttribute: '',
                status: '',
                page: 1,
                pageSize: 10
            });

            // 用户宠物表单
            const userPetForm = ref({
                id: 0,
                userId: null,
                petNo: null,
                exp: 0,
                hp: 100,
                atk: 10,
                def: 10,
                spd: 10,
                status: '牧场',
                isMain: false
            });

            // 计算属性
            const totalPages = computed(() => {
                return Math.ceil(totalCount.value / pageSize.value);
            });

            const visiblePages = computed(() => {
                const current = currentPage.value;
                const total = totalPages.value;
                const pages = [];

                let start = Math.max(1, current - 2);
                let end = Math.min(total, current + 2);

                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }

                return pages;
            });

            // 方法
            const loadUserPets = async () => {
                try {
                    loading.value = true;
                    const requestData = {
                        ...queryForm.value,
                        page: currentPage.value,
                        pageSize: pageSize.value
                    };

                    const response = await axios.post('/UserPet/GetList', requestData);
                    if (response.data.code === 200) {
                        userPetsList.value = response.data.data || [];
                        totalCount.value = response.data.total || 0;
                    }
                } catch (error) {
                    console.error('获取用户宠物列表失败：', error);
                } finally {
                    loading.value = false;
                }
            };

            const loadOptions = async () => {
                try {
                    // 加载用户选项
                    const userResponse = await axios.get('/UserPet/GetUserOptions');
                    if (userResponse.data.code === 200) {
                        userOptions.value = userResponse.data.data;
                    }

                    // 加载宠物配置选项
                    const petResponse = await axios.get('/UserPet/GetPetConfigOptions');
                    if (petResponse.data.code === 200) {
                        petConfigOptions.value = petResponse.data.data || [];
                        filteredPetOptions.value = petConfigOptions.value; // 初始化过滤选项
                    }
                } catch (error) {
                    console.error('加载选项失败：', error);
                }
            };

            const searchUserPets = () => {
                currentPage.value = 1;
                loadUserPets();
            };

            const resetQuery = () => {
                queryForm.value = {
                    userId: null,
                    userName: '',
                    petNo: null,
                    petName: '',
                    petAttribute: '',
                    status: '',
                    page: 1,
                    pageSize: 10
                };
                searchUserPets();
            };

            const changePage = (page) => {
                if (page >= 1 && page <= totalPages.value) {
                    currentPage.value = page;
                    loadUserPets();
                }
            };

            const showAddModal = () => {
                isEdit.value = false;
                userPetForm.value = {
                    id: 0,
                    userId: null,
                    petNo: null,
                    exp: 0,
                    hp: 100,
                    atk: 10,
                    def: 10,
                    spd: 10,
                    status: '牧场',
                    isMain: false
                };
                // 重置宠物搜索状态
                petSearchText.value = '';
                showPetDropdown.value = false;
                filteredPetOptions.value = petConfigOptions.value;
                const modal = new bootstrap.Modal(document.getElementById('userPetModal'));
                modal.show();
            };

            const editUserPet = (userPet) => {
                isEdit.value = true;
                // 正确映射字段名，处理大小写不一致的问题
                userPetForm.value = {
                    id: userPet.id,
                    userId: userPet.userId,
                    petNo: userPet.petNo,
                    exp: userPet.exp,
                    hp: userPet.hp,
                    atk: userPet.atk,
                    def: userPet.def,
                    spd: userPet.spd,
                    status: userPet.status,
                    isMain: userPet.IsMain || userPet.isMain || false  // 处理大小写不一致
                };
                // 设置宠物搜索文本显示当前选择的宠物
                const selectedPet = petConfigOptions.value.find(pet => pet.petNo === userPet.petNo);
                if (selectedPet) {
                    petSearchText.value = `${selectedPet.name} (${selectedPet.attribute})`;
                }
                showPetDropdown.value = false;
                filteredPetOptions.value = petConfigOptions.value;
                const modal = new bootstrap.Modal(document.getElementById('userPetModal'));
                modal.show();
            };

            const saveUserPet = async () => {
                try {
                    saving.value = true;
                    console.log('保存宠物数据:', userPetForm.value);
                    console.log('isMain值:', userPetForm.value.isMain);
                    const url = isEdit.value ? '/UserPet/Update' : '/UserPet/Create';
                    const response = await axios.post(url, userPetForm.value);

                    if (response.data.code === 200) {
                        const modal = bootstrap.Modal.getInstance(document.getElementById('userPetModal'));
                        modal.hide();
                        loadUserPets();
                        alert('保存成功');
                    } else {
                        alert(response.data.message || '保存失败');
                    }
                } catch (error) {
                    console.error('保存失败：', error);
                    alert('保存失败');
                } finally {
                    saving.value = false;
                }
            };

            const deleteUserPet = async (userPet) => {
                if (!confirm(`确定要删除宠物"${userPet.petName}"吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/UserPet/Delete', { id: userPet.id });
                    if (response.data.code === 200) {
                        loadUserPets();
                        alert('删除成功');
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除失败：', error);
                    alert('删除失败');
                }
            };

            const copyUserPet = async (userPet) => {
                if (!confirm(`确定要复制宠物"${userPet.petName}"吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/UserPet/Copy', null, {
                        params: { userPetId: userPet.id }
                    });
                    if (response.data.code === 200) {
                        loadUserPets();
                        alert(response.data.message || '复制成功');
                    } else {
                        alert(response.data.message || '复制失败');
                    }
                } catch (error) {
                    console.error('复制失败：', error);
                    alert('复制失败');
                }
            };

            const viewDetail = (userPet) => {
                selectedUserPet.value = userPet;
                const modal = new bootstrap.Modal(document.getElementById('detailModal'));
                modal.show();
            };

            const closeModal = () => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('userPetModal'));
                if (modal) modal.hide();
            };

            const closeDetailModal = () => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('detailModal'));
                if (modal) modal.hide();
            };

            const calculateLevel = (exp) => {
                return Math.floor(exp / 100) + 1;
            };

            // 宠物搜索相关方法
            const filterPetOptions = () => {
                const searchTerm = petSearchText.value.toLowerCase();
                if (!searchTerm) {
                    filteredPetOptions.value = petConfigOptions.value;
                } else {
                    filteredPetOptions.value = petConfigOptions.value.filter(pet =>
                        pet.name.toLowerCase().includes(searchTerm) ||
                        pet.attribute.toLowerCase().includes(searchTerm)
                    );
                }
            };

            const selectPet = (pet) => {
                userPetForm.value.petNo = pet.petNo;
                petSearchText.value = `${pet.name} (${pet.attribute})`;
                showPetDropdown.value = false;
            };

            const togglePetDropdown = () => {
                showPetDropdown.value = !showPetDropdown.value;
                if (showPetDropdown.value) {
                    filterPetOptions();
                }
            };

            const hidePetDropdown = () => {
                setTimeout(() => {
                    showPetDropdown.value = false;
                }, 150);
            };

            // 处理主战宠物变化
            const handleMainPetChange = (event) => {
                console.log('主战宠物状态变化:', event.target.checked);
                console.log('变化前的isMain值:', userPetForm.value.isMain);
                userPetForm.value.isMain = event.target.checked;
                console.log('变化后的isMain值:', userPetForm.value.isMain);

                // 强制更新DOM元素的checked状态
                nextTick(() => {
                    const checkbox = document.getElementById('isMainCheck');
                    if (checkbox) {
                        checkbox.checked = userPetForm.value.isMain;
                        console.log('DOM复选框状态已更新为:', checkbox.checked);
                    }
                });
            };

            // 监听isMain字段变化，确保DOM同步
            watch(() => userPetForm.value.isMain, (newValue) => {
                console.log('Watch检测到isMain变化:', newValue);
                nextTick(() => {
                    const checkbox = document.getElementById('isMainCheck');
                    if (checkbox) {
                        checkbox.checked = newValue;
                        console.log('Watch强制更新DOM复选框状态为:', checkbox.checked);
                    }
                });
            });

            // 生命周期
            onMounted(async () => {
                await loadOptions();
                await loadUserPets();
            });

            return {
                // 数据
                loading,
                saving,
                userPetsList,
                totalCount,
                currentPage,
                pageSize,
                isEdit,
                selectedUserPet,
                userOptions,
                petConfigOptions,
                queryForm,
                userPetForm,

                // 宠物搜索相关数据
                petSearchText,
                showPetDropdown,
                filteredPetOptions,

                // 计算属性
                totalPages,
                visiblePages,

                // 方法
                loadUserPets,
                searchUserPets,
                resetQuery,
                changePage,
                showAddModal,
                editUserPet,
                saveUserPet,
                deleteUserPet,
                copyUserPet,
                viewDetail,
                closeModal,
                closeDetailModal,
                calculateLevel,

                // 宠物搜索相关方法
                filterPetOptions,
                selectPet,
                togglePetDropdown,
                hidePetDropdown,

                // 主战宠物处理方法
                handleMainPetChange
            };
        }
    }).mount('#userPetApp');
</script>